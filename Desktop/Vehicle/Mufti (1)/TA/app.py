from flask import Flask, render_template, Response, request, jsonify, session
import cv2
import threading
import time
import pandas as pd
from ultralytics import YOLO
import cvzone
from tracker import*
import tkinter as tk
from tkinter import simpledialog
import os
from datetime import datetime
import secrets

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', secrets.token_hex(16))

ADMIN_USERNAME = "Admin"
ADMIN_PASSWORD = "admin"
confident_treshold = 0.3

gst_pipeline1 = (
    "v4l2src device=/dev/video0 ! "
    "image/jpeg, width=1280, height=720, framerate=30/1 ! "
    "jpegdec ! videoconvert ! appsink"
)

gst_pipeline2 = (
    "v4l2src device=/dev/video2 ! "
    "image/jpeg, width=1280, height=720, framerate=30/1 ! "
    "jpegdec ! videoconvert ! appsink"
)
# gst_pipeline2 = (
#     "v4l2src device=/dev/video4 ! "
#     "video/x-h264, width=1280, height=720, framerate=30/1 ! "
#     "h264parse ! avdec_h264 ! videoconvert ! appsink"
# )

@app.route('/slots')
def slots():
    return jsonify({
        "available_slotA": video_states['A']['slot_tersedia'],
        "available_slotB": video_states['B']['slot_tersedia']
    })

@app.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
        session['is_admin'] = True
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': 'Invalid credentials'})

# Rute logout
@app.route('/logout', methods=['POST'])
def logout():
    session.pop('is_admin', None)
    return jsonify({'success': True})

panjang_tampilan = 640
tinggi_tampilan = 480

frame_config = {
    'panjang_tampilan': panjang_tampilan,
    'tinggi_tampilan': tinggi_tampilan,
    'original_width': 1280,  # akan diupdate dengan ukuran asli
    'original_height': 720   # akan diupdate dengan ukuran asli
}


#videos = {
  #  'A': {'path': '/home/<USER>/my_video-11.mkv', 'cap': None, 'points': []},
 #   'B': {'path': '/home/<USER>/my_video-9.mkv', 'cap': None, 'points': []}
#}

# videos = {
# 'A': {'path': gst_pipeline1, 'cap': None, 'points': []},
# 'B': {'path': gst_pipeline2, 'cap': None, 'points': []}
# }

videos = {
    'A': {'path': '/home/<USER>/Downloads/my_video-9.mkv', 'cap': None, 'points': []},
    'B': {'path': '/home/<USER>/Downloads/my_video-11.mkv', 'cap': None, 'points': []}
}

video_states = {
    'A': {
        'mouse_active': False,
        'mouse_mode': 'y',
        'mouse_pressed': False,
        'titik_awal_kotak': (0, 0),
        'titik_akhir_kotak': (0, 0),
        'titik_sudut': [],
        'kotak_parkir': [],
        'mode_crop': False,
        'area_crop': (0, 0, 0, 0),
        'kendaraan_masuk': 0,
        'kendaraan_keluar': 0,
        'id_terlacak': set(),
        'id_masuk': set(),  # Set untuk menyimpan ID yang masuk
        'id_keluar': set(),   # Set untuk menyimpan ID yang keluar
        'kotak_mobil': [],
        'slot_tersedia' : 0,
        'data_parkir': [],
        'id_objek' : set(),
        'kotak_kendaraan' : [],
        'tracker' : Tracker(),
        'dict_id_saat_ini' : {},
        'objek' : [],
        'waktu_sebelum': time.time()
    },
    'B': {
        'mouse_active': False,
        'mouse_mode': 'y',
        'mouse_pressed': False,
        'titik_awal_kotak': (0, 0),
        'titik_akhir_kotak': (0, 0),
        'titik_sudut': [],
        'kotak_parkir': [],
        'mode_crop': False,
        'area_crop': (0, 0, 0, 0),
        'kendaraan_masuk': 0,
        'kendaraan_keluar': 0,
        'id_terlacak': set(),
        'id_masuk': set(),  # Set untuk menyimpan ID yang masuk
        'id_keluar': set(),   # Set untuk menyimpan ID yang keluar
        'kotak_mobil': [],
        'slot_tersedia' : 0,
        'data_parkir': [],
        'id_objek' : set(),
        'kotak_kendaraan' : [],
        'tracker' : Tracker(),
        'dict_id_saat_ini' : {},
        'objek' : [],
        'waktu_sebelum': time.time()
    }
}

video_locks = {video_id: threading.Lock() for video_id in videos}

input_slot = 0
mouse_pressed = False
prev_mouse_pressed = False
titik_awal_kotak = (0, 0)
titik_akhir_kotak = (0, 0)
kotak_parkir = []
mouse_active = False
titik_sudut = []
mouse_mode = "y"
kendaraan_masuk = 0
kendaraan_keluar = 0
id_terlacak = set()
kotak_mobil = []
cy1 = 184
cy2 = 209
offset = 8
waktu_loop = 0
dict_umur_id = {}
area_crop = (0,0,0,0)
mode_crop = False
titik_awal_crop = (0, 0)
titik_akhir_crop = (0, 0)
jumlah = 0
jumlah_mobil = 0
jumlah_bus = 0
jumlah_truk = 0
cy1 = 184
cy2 = 209
offset = 8
waktu_loop = 0
dict_umur_id = {}
kotak_mobil = []

statistik_parkir = {
    'spot_tersedia': 0,
    'kendaraan_masuk': 0,
    'kendaraan_keluar': 0
}

def ubah_status(state, target_id, status):
    for slot in state['data_parkir']:
        if slot['id'] == target_id:
            slot['status'] = status
            break

def bersihkan_area_parkir(video_id):
    if video_id in video_states:
        video_states[video_id]['data_parkir'].clear()
    else:
        print(f"Area {video_id} tidak ditemukan.")

def perbarui_data_parkir(video_id, total):
    if video_id in video_states:
        video_states[video_id]['data_parkir'] = []
        for i in range(1, total + 1):
            new_id = f"{video_id}{i}"
            video_states[video_id]['data_parkir'].append({'id': new_id, 'status': 'Tersedia'})
    else:
        print(f"Area {video_id} tidak ditemukan.")

def update_data_parkir(video_id, perubahan):
    state = video_states[video_id]
    if perubahan > 0:
        if state['data_parkir']:
            id_terakhir = state['data_parkir'][-1]['id']
            nomor_terakhir = int(id_terakhir)
        else:
            nomor_terakhir = 0

        for i in range(1, perubahan + 1):
            id_baru = f"{nomor_terakhir + i}"
            state['data_parkir'].append({'id': id_baru, 'status': 'Tersedia'})

    elif perubahan < 0:
        untuk_dihapus = min(abs(perubahan), len(state['data_parkir']))
        state['data_parkir'] = state['data_parkir'][:-untuk_dihapus]

def cari_spot_kosong(video_id):
    if video_id not in video_states:
        print(f"Area {video_states} tidak ditemukan.")
        return 0

    state = video_states[video_id]
    return sum(1 for spot in state['data_parkir'] if spot['status'] == 'Tersedia')

def apakah_titik_dalam_kotak(px, py, koordinat_kotak):
    x1, y1, x2, y2, x3, y3, x4, y4 = koordinat_kotak
    def hasil_perkalian_silang(x1, y1, x2, y2, x3, y3):
        return (x2 - x1) * (y3 - y1) - (y2 - y1) * (x3 - x1)

    d1 = hasil_perkalian_silang(x1, y1, x2, y2, px, py)
    d2 = hasil_perkalian_silang(x2, y2, x3, y3, px, py)
    d3 = hasil_perkalian_silang(x3, y3, x4, y4, px, py)
    d4 = hasil_perkalian_silang(x4, y4, x1, y1, px, py)

    # If the signs of all cross products are the same, the point is inside
    return (d1 >= 0 and d2 >= 0 and d3 >= 0 and d4 >= 0) or (d1 <= 0 and d2 <= 0 and d3 <= 0 and d4 <= 0)

def urutkan_titik_sudut(titik):
    titik_berdasar_y = sorted(titik, key=lambda p: p[1])
    titik_atas = titik_berdasar_y[:2]
    titik_bawah = titik_berdasar_y[2:]

    titik_atas.sort(key=lambda p: p[0])
    titik_bawah.sort(key=lambda p: p[0], reverse=True)
    return [titik_atas[0], titik_atas[1], titik_bawah[0], titik_bawah[1]]

def simpan_kotak2(video_id, input_slot):
    state = video_states[video_id]
    if len(state['titik_sudut']) == 4:
        # Mengurutkan corner points sebelum memproses
        sudut_terurut = urutkan_titik_sudut(state['titik_sudut'])
        filename = f"data2_{video_id}.ul"
        if not os.path.exists(filename):
            # Jika file tidak ada, buat baru
            with open(filename, "w") as file:
                for x in range(input_slot):
                    x1 = sudut_terurut[0][0] + (sudut_terurut[1][0] - sudut_terurut[0][0]) // input_slot * x
                    x2 = sudut_terurut[0][0] + (sudut_terurut[1][0] - sudut_terurut[0][0]) // input_slot * (x + 1)
                    x3 = sudut_terurut[3][0] + (sudut_terurut[2][0] - sudut_terurut[3][0]) // input_slot * (x + 1)
                    x4 = sudut_terurut[3][0] + (sudut_terurut[2][0] - sudut_terurut[3][0]) // input_slot * x
                    y1 = sudut_terurut[0][1] + (sudut_terurut[1][1] - sudut_terurut[0][1]) // input_slot * x
                    y2 = sudut_terurut[0][1] + (sudut_terurut[1][1] - sudut_terurut[0][1]) // input_slot * (x + 1)
                    y3 = sudut_terurut[3][1] + (sudut_terurut[2][1] - sudut_terurut[3][1]) // input_slot * (x + 1)
                    y4 = sudut_terurut[3][1] + (sudut_terurut[2][1] - sudut_terurut[3][1]) // input_slot * x
                    file.write(f"{x1},{y1},{x2},{y2},{x3},{y3},{x4},{y4}\n")
        else:
            # Jika file sudah ada, tambahkan data baru
            with open(filename, "a") as file:
                # print(f"user : found ...")
                for x in range(input_slot):
                    x1 = sudut_terurut[0][0] + (sudut_terurut[1][0] - sudut_terurut[0][0]) // input_slot * x
                    x2 = sudut_terurut[0][0] + (sudut_terurut[1][0] - sudut_terurut[0][0]) // input_slot * (x + 1)
                    x3 = sudut_terurut[3][0] + (sudut_terurut[2][0] - sudut_terurut[3][0]) // input_slot * (x + 1)
                    x4 = sudut_terurut[3][0] + (sudut_terurut[2][0] - sudut_terurut[3][0]) // input_slot * x
                    y1 = sudut_terurut[0][1] + (sudut_terurut[1][1] - sudut_terurut[0][1]) // input_slot * x
                    y2 = sudut_terurut[0][1] + (sudut_terurut[1][1] - sudut_terurut[0][1]) // input_slot * (x + 1)
                    y3 = sudut_terurut[3][1] + (sudut_terurut[2][1] - sudut_terurut[3][1]) // input_slot * (x + 1)
                    y4 = sudut_terurut[3][1] + (sudut_terurut[2][1] - sudut_terurut[3][1]) // input_slot * x
                    file.write(f"{x1},{y1},{x2},{y2},{x3},{y3},{x4},{y4}\n")
    else:
        filename = f"data2_{video_id}.ul"
        if not os.path.exists(filename):
            kotak_default = (100, 100, 200, 100, 200, 200, 100, 200)  # Default coordinates for one parking slot
            with open(filename, "w") as file:
                file.write(f"{','.join(map(str, kotak_default))}")

def simpan_ukuran_crop(video_id):
    state = video_states[video_id]
    x1 = min(state['titik_awal_kotak'][0], state['titik_akhir_kotak'][0])
    y1 = min(state['titik_awal_kotak'][1], state['titik_akhir_kotak'][1])
    x2 = max(state['titik_awal_kotak'][0], state['titik_akhir_kotak'][0])
    y2 = max(state['titik_awal_kotak'][1], state['titik_akhir_kotak'][1])
    if ((x2-x1) ** 2 + (y2-y1) ** 2) ** 0.5 < 100:
        x2 = x1 + 100
        y2 = y1 + 100

    state['area_crop'] = (x1, y1, x2, y2)

    filename = f"crop_{video_id}.ul"
    with open(filename, "w") as file:
        file.write(f"{x1},{y1},{x2},{y2}")

def apakah_objek_dalam_kotak(video_id, data_persegi):
    for bbox in video_states[video_id]['kotak_kendaraan']:
        lebar = bbox[2] - bbox[0]
        tinggi = bbox[3] - bbox[1]

        # Calculate object center
        pusat_x = bbox[0] + lebar // 2
        pusat_y = bbox[1] + tinggi // 2

        # Get frame width
        lebar_frame = frame_config['original_width']
        toleransi_x = lebar//3
        toleransi_y = tinggi//8
        jarak_y = tinggi//2

        # Determine check points based on object center position
        if pusat_x < lebar_frame // 3:
            # Object center is in left third - check points from center to bottom-left corner
            jarak_x = pusat_x-(bbox[0]+toleransi_x)
            tujuan_x = bbox[0]+toleransi_x
            titik_cek = [
                (pusat_x, pusat_y),                           # Object center
                (tujuan_x+jarak_x//3, pusat_y+jarak_y//3),                            # Left edge at center height
                (tujuan_x+jarak_x*2//3, pusat_y+jarak_y*2//3),                            # Bottom edge at center width
                (tujuan_x, bbox[3])                              # Bottom-left corner
            ]
        elif pusat_x > 2 * lebar_frame // 3:
            # Object center is in right third - check points from center to bottom-right corner
            jarak_x = bbox[1]-(pusat_x+toleransi_x)
            tujuan_x = bbox[2]-toleransi_x
            titik_cek = [
                (pusat_x, pusat_y),                           # Object center
                (tujuan_x-2*jarak_x//3, pusat_y+jarak_y//3),                            # Right edge at center height
                (tujuan_x-jarak_x//3, pusat_y+jarak_y*2//3),                            # Bottom edge at center width
                (tujuan_x, bbox[3]-toleransi_y)                              # Bottom-right corner
            ]
        else:
            # Object center is in middle third - check points from center to bottom-center
            titik_cek = [
                (pusat_x, pusat_y),                           # Object center
                (pusat_x, pusat_y+jarak_y//3),               # Bottom-left quarter
                (pusat_x, pusat_y+jarak_y*2//3),                            # Bottom center
                (pusat_x, bbox[3]-toleransi_y)                # Bottom-right quarter
            ]

        for titik in titik_cek:
            if apakah_titik_dalam_kotak(titik[0], titik[1], data_persegi):
                return True

    return False

# model = YOLO('../yolov8n_ncnn_model')
model = YOLO('yolov8n.pt')
with open("coco.txt", "r") as my_file:
    class_list = my_file.read().split("\n")


def init_video(video_id):
    if videos[video_id]['cap'] is None:
        
        videos[video_id]['cap'] = cv2.VideoCapture(videos[video_id]['path'])       
        # videos[video_id]['cap'] = cv2.VideoCapture(videos[video_id]['path'], cv2.CAP_GSTREAMER)
        
        cap = videos[video_id]['cap']
        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        frame_config['width'] = actual_width
        frame_config['height'] = actual_height

def resize_frame(frame):
    """Resize frame ke ukuran display yang ditentukan"""
    return cv2.resize(frame, (panjang_tampilan, tinggi_tampilan))

def scale_coordinates(x, y, from_width, from_height, to_width, to_height):
    """Scale koordinat dari satu resolusi ke resolusi lain"""
    scale_x = to_width / from_width
    scale_y = to_height / from_height
    
    scaled_x = int(x * scale_x)
    scaled_y = int(y * scale_y)
    
    return scaled_x, scaled_y

def inisialisasi_kotak2(video_id):
    state = video_states[video_id]
    state['kotak_parkir'] = []
    state['data_parkir'] = []
    filename = f"data2_{video_id}.ul"

    if os.path.exists(filename):
        with open(filename, "r") as file:
            for line in file:
                x1, y1, x2, y2, x3, y3, x4, y4 = map(int, line.strip().split(","))
                state['kotak_parkir'].append((x1, y1, x2, y2, x3, y3, x4, y4))
    else:
        # Create default parking area if file doesn't exist
        kotak_default = (100, 100, 200, 100, 200, 200, 100, 200)  # Default coordinates for one parking slot
        state['kotak_parkir'].append(kotak_default)
        with open(filename, "w") as file:
            file.write(f"{','.join(map(str, kotak_default))}\n")
    perbarui_data_parkir(video_id, len(state['kotak_parkir']));

def inisialisasi_area_crop(video_id):
    state = video_states[video_id]
    filename = f"crop_{video_id}.ul"

    if os.path.exists(filename):
        state['mode_crop'] = True
        with open(filename, "r") as file:
            for line in file:
                x1, y1, x2, y2 = map(int, line.strip().split(","))
                state['area_crop'] = (x1, y1, x2, y2)
    else:
        # Create default crop area if file doesn't exist
        crop_default = (50, 50, 550, 450)  # Default coordinates for crop area
        state['area_crop'] = crop_default
        state['mode_crop'] = True
        with open(filename, "w") as file:
            file.write(f"{','.join(map(str, crop_default))}")


def proses_frame(state, frame):
    # Tambahkan variabel untuk FPS
    # waktu_saat_ini = time.time()
    # fps = 1.0 / (waktu_saat_ini - state.get('waktu_sebelum', waktu_saat_ini))
    # state['waktu_sebelum'] = waktu_saat_ini

    # Proses frame seperti biasa
    if state['mode_crop']:
        x1_crop, y1_crop, x2_crop, y2_crop = state['area_crop']
        frame_terpotong = frame[y1_crop:y2_crop, x1_crop:x2_crop]
        if frame_terpotong is None or frame_terpotong.size == 0:
            print("Warning: Cropped frame is empty. Skipping inference...")
            return frame
        else:
            hasil = model.predict(frame_terpotong)
    else:
        hasil = model.predict(frame)

    # cv2.putText(frame, f"FPS: {int(fps)}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX,
    #             1, (0, 255, 0), 2)

    return frame

def pemrosesan_frame_berkala(video_id, interval=2):  # interval dalam detik
    global kotak_mobil, kendaraan_masuk, kendaraan_keluar, id_terlacak, jumlah_mobil, confident_treshold
    inisialisasi_kotak2(video_id)
    inisialisasi_area_crop(video_id)
    state = video_states[video_id]
    while True:
        with video_locks[video_id]:
            init_video(video_id)
            cap = videos[video_id]['cap']
            success, frame = cap.read()
            if not success:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue

            if frame_config['original_width'] != int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)) or \
               frame_config['original_height'] != int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)):
                frame_config['original_width'] = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                frame_config['original_height'] = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            if state['mode_crop']:
                x1_crop, y1_crop, x2_crop, y2_crop = state['area_crop']
                frame_terpotong = frame[y1_crop:y2_crop, x1_crop:x2_crop]
                if frame_terpotong is None or frame_terpotong.size == 0:
                    continue
            else:
                frame_terpotong = frame

            hasil = model.predict(frame_terpotong)
            deteksi = hasil[0].boxes.data
            px = pd.DataFrame(deteksi).astype("float")

            state['objek'] = []
            for index, row in px.iterrows():
                x1 = int(state['area_crop'][0] + row[0])
                y1 = int(state['area_crop'][1] + row[1])
                x2 = int(state['area_crop'][0] + row[2])
                y2 = int(state['area_crop'][1] + row[3])
                d = int(row[5])
                kepercayaan = float(row[4])
                c = class_list[d]
                if kepercayaan > confident_treshold:
                    if 'car' in c or 'bus' in c or 'truck' in c:
                        state['objek'].append([x1, y1, x2, y2, kepercayaan])

            state['kotak_kendaraan'] = state['tracker'].update(state['objek'])

            for bbox in state['kotak_kendaraan']:
                cx = int((bbox[0] + bbox[2]) / 2)
                cy = int((bbox[1] + bbox[3]) / 2)
                if (cy > cy1 - offset) and (cy < cy1 + offset):
                    jumlah_mobil += 1

            for bbox in state['kotak_kendaraan']:
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 255), 2)
                cvzone.putTextRect(frame, f'{bbox[5]}, {bbox[4]:.2f}', (bbox[0], bbox[1]), 1, 1)

            index = 0
            prev = False
            for slot_parkir in state['kotak_parkir']:
                if apakah_objek_dalam_kotak(video_id, slot_parkir):
                    ubah_status(state, f"{video_id}{index + 1}", "Terisi")
                    cv2.line(frame, (slot_parkir[0], slot_parkir[1]), (slot_parkir[6], slot_parkir[7]), (255, 0, 0), 3)
                    for x in range(0, 5, 2):
                        cv2.line(frame, (slot_parkir[x], slot_parkir[x+1]), (slot_parkir[x+2], slot_parkir[x+3]), (255, 0, 0), 3)
                else:
                    ubah_status(state, f"{video_id}{index + 1}", "Tersedia")
                    cv2.line(frame, (slot_parkir[0], slot_parkir[1]), (slot_parkir[6], slot_parkir[7]), (0, 0, 255), 3)
                    for x in range(0, 5, 2):
                        cv2.line(frame, (slot_parkir[x], slot_parkir[x+1]), (slot_parkir[x+2], slot_parkir[x+3]), (0, 0, 255), 3)

                # Ubah cara mengakses data_parkir
                if index < len(state['data_parkir']):
                    id_slot = f"{video_id}{index + 1}"  # Membuat ID seperti A1, A2, dst
                    cv2.putText(frame, id_slot,
                              (slot_parkir[6] + (slot_parkir[4] - slot_parkir[6]) // 2, slot_parkir[7]),
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2, cv2.LINE_AA)
                index += 1

            for titik in state['titik_sudut']:
                cv2.circle(frame, titik, 5, (0, 0, 255), -1)

            state['id_objek'] = set((obj[5], obj[6]) for obj in state['kotak_kendaraan'])
            id_saat_ini_saja = set(obj[5] for obj in state['kotak_kendaraan'])  # Set yang hanya berisi ID

            # Masuk kendaraan baru
            for id_obj, umur_obj in state['id_objek']:
                if id_obj not in state['id_masuk'] and umur_obj > 5:
                    state['id_masuk'].add(id_obj)  # Tambahkan ke set ID yang masuk
                    state['kendaraan_masuk'] = len(state['id_masuk'])  # Update jumlah kendaraan masuk
                    print(f"ID {id_obj} entered in {video_id}. Total vehicles entered: {state['kendaraan_masuk']}")

            # Kendaraan yang keluar - hanya jika ID benar-benar tidak ada lagi
            for id_obj in list(state['id_masuk']):
                if id_obj not in id_saat_ini_saja:  # ID tidak ada di frame saat ini
                    if id_obj not in state['id_keluar']:  # dan belum tercatat sebagai keluar
                        state['id_keluar'].add(id_obj)  # Tambahkan ke set ID yang keluar
                        state['kendaraan_keluar'] = len(state['id_keluar'])  # Update jumlah kendaraan keluar
                        print(f"ID {id_obj} leaved from {video_id}. Total vehicles leaved: {state['kendaraan_keluar']}")

            # Jika ID yang sudah tercatat keluar muncul lagi, hapus dari daftar keluar
            for id_obj in list(state['id_keluar']):
                if id_obj in id_saat_ini_saja:
                    state['id_keluar'].remove(id_obj)
                    state['kendaraan_keluar'] = len(state['id_keluar'])  # Update jumlah kendaraan keluar
                    print(f"ID {id_obj} returned to {video_id}. Total vehicles leaved: {state['kendaraan_keluar']}")

            cv2.putText(frame, f"Masuk: {state['kendaraan_masuk']}", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Keluar: {state['kendaraan_keluar']}", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

            for titik in videos[video_id]['points']:
                x, y = titik['x'], titik['y']
                cv2.circle(frame, (x, y), 5, (0, 0, 255), -1)

            frame_terproses = proses_frame(state, frame)
            state['last_frame'] = resize_frame(frame_terproses)

        time.sleep(interval)

def generate_frames(video_id):
    while True:
        with video_locks[video_id]:
            frame = video_states[video_id].get('last_frame', None)
            if frame is None:
                continue

            ret, buffer = cv2.imencode('.jpg', frame)
            frame = buffer.tobytes()

            video_states[video_id]['slot_tersedia'] = cari_spot_kosong(video_id)

        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
        time.sleep(1)

@app.route('/')
def index():
    return render_template('index.html', 
                         frame_width=frame_config['panjang_tampilan'],
                         frame_height=frame_config['tinggi_tampilan'],
                         session=session)

@app.route('/video_feed/<video_id>')
def video_feed(video_id):
    print(f"[INFO] /video_feed/{video_id} dipanggil")
    return Response(generate_frames(video_id), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/mark_point/<video_id>', methods=['POST'])
def mark_point(video_id):
    data = request.get_json()
    x = int(data['x'])
    y = int(data['y'])
    with video_locks[video_id]:
        videos[video_id]['points'].append({'x': x, 'y': y})
    return jsonify({'message': 'Point marked successfully'})

@app.route('/video_dimensions')
def video_dimensions():
    return jsonify(frame_config)

@app.route('/mouse_event', methods=['POST'])
def handle_mouse_event():
    """Handle mouse events from web interface"""
    try:
        data = request.get_json()
        video_id = data.get('videoId')
        event_type = data.get('event')
        mouse_mode = data.get('mouse_mode', 'y')  # Default to 'y' if not provided

        if video_id not in video_states:
            return jsonify({'success': False, 'message': 'Invalid video ID'}), 400

        state = video_states[video_id]

        # Update mouse mode from request
        state['mouse_mode'] = mouse_mode

        if not state['mouse_active'] and mouse_mode != 'y':
            state['mouse_active'] = True

        if mouse_mode == 'y':
            state['mouse_active'] = False
            return jsonify({'success': True, 'message': 'Mouse not active'})

        display_x = int(data.get('x'))
        display_y = int(data.get('y'))

        x, y = scale_coordinates(
            display_x, display_y,
            frame_config['panjang_tampilan'], frame_config['tinggi_tampilan'],
            frame_config['original_width'], frame_config['original_height']
        )

        response_data = {
            'success': True,
            'refresh': False,
            'message': ''
        }

        if state['mouse_mode'] == "o":
            if event_type == 'click':
                state['titik_sudut'].append((x, y))
                response_data['message'] = f'Point added: {len(state["titik_sudut"])}/4'

                if len(state['titik_sudut']) == 4:
                    response_data['refresh'] = True
                    response_data['action'] = 'request_slot_input'
                    response_data['videoId'] = video_id

                elif len(state['titik_sudut']) > 4:
                    state['titik_sudut'].pop(0)

        elif state['mouse_mode'] == 'y':
            if video_states['A']['titik_sudut']:
                video_states['A']['titik_sudut'].pop(0)
            if video_states['B']['titik_sudut']:
                video_states['B']['titik_sudut'].pop(0)

        elif state['mouse_mode'] == "d":
            if video_states['A']['titik_sudut']:
                video_states['A']['titik_sudut'].pop(0)
            if video_states['B']['titik_sudut']:
                video_states['B']['titik_sudut'].pop(0)
            if event_type == 'click':
                for slot_parkir in state['kotak_parkir']:
                    if apakah_titik_dalam_kotak(x, y, slot_parkir):
                        state['kotak_parkir'].remove(slot_parkir)
                        filename = f"data2_{video_id}.ul"
                        with open(filename, "w") as file:
                            for kotak in state['kotak_parkir']:
                                file.write(f"{','.join(map(str, kotak))}\n")
                        perbarui_data_parkir(video_id, len(state['kotak_parkir']))
                        response_data['refresh'] = True
                        response_data['message'] = 'Parking slot deleted'
                        break

        elif state['mouse_mode'] == "c":
            if video_states['A']['titik_sudut']:
                video_states['A']['titik_sudut'].pop(0)
            if video_states['B']['titik_sudut']:
                video_states['B']['titik_sudut'].pop(0)
            if event_type == 'mousedown':
                state['mouse_pressed'] = True
                state['titik_awal_kotak'] = (x, y)
                response_data['message'] = 'Started cropping'

            elif event_type == 'mousemove' and state['mouse_pressed']:
                state['titik_akhir_kotak'] = (x, y)
                display_start_x, display_start_y = scale_coordinates(
                    state['titik_awal_kotak'][0], state['titik_awal_kotak'][1],
                    frame_config['original_width'], frame_config['original_height'],
                    frame_config['panjang_tampilan'], frame_config['tinggi_tampilan']
                )
                display_end_x, display_end_y = scale_coordinates(
                    state['titik_akhir_kotak'][0], state['titik_akhir_kotak'][1],
                    frame_config['original_width'], frame_config['original_height'],
                    frame_config['panjang_tampilan'], frame_config['tinggi_tampilan']
                )
                response_data['box'] = {
                    'start': (display_start_x, display_start_y),
                    'end': (display_end_x, display_end_y)
                }

            elif event_type == 'mouseup' or event_type == 'click':
                state['mouse_pressed'] = False
                state['titik_akhir_kotak'] = (x, y)
                simpan_ukuran_crop(video_id)
                inisialisasi_area_crop(video_id)
                state['mode_crop'] = True
                response_data['refresh'] = True
                response_data['message'] = 'Crop area saved'

        print(f"mouse_mode: {state['mouse_mode']}, data A : {video_states['A']['titik_sudut']} data B : {video_states['B']['titik_sudut']}")
        return jsonify(response_data)
    except Exception as e:
        app.logger.error(f"Error in mouse_event: {str(e)}")
        return jsonify({'success': False, 'message': f'Server error: {str(e)}'}), 500


@app.route('/set_slot_input', methods=['POST'])
def set_slot_input():
    """Handle slot input from web interface"""
    data = request.get_json()
    video_id = data.get('videoId')
    input_slot = int(data.get('slots', 1))

    if video_id not in video_states:
        return jsonify({'success': False, 'message': 'Invalid video ID'}), 400

    state = video_states[video_id]
    simpan_kotak2(video_id, input_slot)
    inisialisasi_kotak2(video_id)
    state['titik_sudut'].clear()

    return jsonify({
        'success': True,
        'message': f'Created {input_slot} parking slots for {video_id}'
    })

@app.route('/set_mouse_mode', methods=['POST'])
def set_mouse_mode():
    data = request.get_json()
    video_id = data.get('videoId')
    mode = data.get('mode')
    
    if video_id not in video_states:
        return jsonify({'success': False, 'message': 'Invalid video ID'}), 400
    if mode in ['c', 'o', 'd']:
        video_states[video_id]['mouse_mode'] = mode
        video_states[video_id]['mouse_active'] = True
        return jsonify({
            'success': True, 
            'message': f"Mouse mode set to {mode} for {video_id}"
        })
    elif mode == 'y':
        video_states[video_id]['mouse_mode'] = mode
        video_states[video_id]['mouse_active'] = False
        return jsonify({
            'success': True, 
            'message': f"Mouse mode disabled for {video_id}"
        })
    else:
        return jsonify({'success': False, 'message': 'Invalid mode'}), 400
    
@app.route('/get_stats')
def get_stats():
    stats = {
        'A': {
            'slot_tersedia': video_states['A']['slot_tersedia'],
            'kendaraan_masuk': video_states['A']['kendaraan_masuk'],
            'kendaraan_keluar': video_states['A']['kendaraan_keluar']
        },
        'B': {
            'slot_tersedia': video_states['B']['slot_tersedia'],
            'kendaraan_masuk': video_states['B']['kendaraan_masuk'],
            'kendaraan_keluar': video_states['B']['kendaraan_keluar']
        }
    }
    return jsonify(stats)

@app.route("/api/parking/<video_id>")
def api_parking(video_id):
    data = video_states[video_id]['data_parkir']
    return jsonify({
        "spots": data
    })

@app.route("/api/parking/delete", methods=['POST'])
def api_parking_delete():
    data = request.get_json()
    spot_id = data.get('spotId')

    video_id = spot_id[0]
    index_id = int(spot_id[1:])

    if video_id not in video_states:
        return jsonify({'success': False, 'message': 'Invalid video ID'}), 400

    state = video_states[video_id]

    state['data_parkir'] = [spot for spot in state['data_parkir'] if spot['id'] != spot_id]

    if 0 <= index_id - 1 < len(state['kotak_parkir']):
        del state['kotak_parkir'][index_id - 1]

        filename = f"data2_{video_id}.ul"
        with open(filename, "w") as file:
            for kotak in state['kotak_parkir']:
                file.write(f"{','.join(map(str, kotak))}\n")

        perbarui_data_parkir(video_id, len(state['kotak_parkir']))

        return jsonify({'success': True, 'message': f'Spot {spot_id} deleted'})
    else:
        return jsonify({'success': False, 'message': f'Invalid index {index_id} for video {video_id}'}), 400

if __name__ == '__main__':
    # app.run(debug=True, host='0.0.0.0', port=5000)
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        for vid in videos:
            threading.Thread(target=pemrosesan_frame_berkala, args=(vid,), daemon=True).start()
    # app.run(debug=False)
    app.run(debug=True, host='0.0.0.0', port=5000)
