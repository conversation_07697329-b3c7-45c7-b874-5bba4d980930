from flask import Flask, render_template, Response, request, jsonify, session
import cv2
import threading
import time
import pandas as pd
from ultralytics import YOLO
import cvzone
from tracker import*
import tkinter as tk
from tkinter import simpledialog
import os
from datetime import datetime
import secrets

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', secrets.token_hex(16))

ADMIN_USERNAME = "Admin"
ADMIN_PASSWORD = "admin"
confident_treshold = 0.3

gst_pipeline1 = (
    "v4l2src device=/dev/video0 ! "
    "image/jpeg, width=1280, height=720, framerate=30/1 ! "
    "jpegdec ! videoconvert ! appsink"
)

gst_pipeline2 = (
    "v4l2src device=/dev/video2 ! "
    "image/jpeg, width=1280, height=720, framerate=30/1 ! "
    "jpegdec ! videoconvert ! appsink"
)
# gst_pipeline2 = (
#     "v4l2src device=/dev/video4 ! "
#     "video/x-h264, width=1280, height=720, framerate=30/1 ! "
#     "h264parse ! avdec_h264 ! videoconvert ! appsink"
# )

@app.route('/slots')
def slots():
    return jsonify({
        "available_slotA": video_states['A']['available_slot'],
        "available_slotB": video_states['B']['available_slot']
    })

@app.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
        session['is_admin'] = True
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': 'Invalid credentials'})

# Rute logout
@app.route('/logout', methods=['POST'])
def logout():
    session.pop('is_admin', None)
    return jsonify({'success': True})

DISPLAY_WIDTH = 640
DISPLAY_HEIGHT = 480

frame_config = {
    'display_width': DISPLAY_WIDTH,
    'display_height': DISPLAY_HEIGHT,
    'original_width': 1280,  # akan diupdate dengan ukuran asli
    'original_height': 720   # akan diupdate dengan ukuran asli
}


#videos = {
  #  'A': {'path': '/home/<USER>/my_video-11.mkv', 'cap': None, 'points': []},
 #   'B': {'path': '/home/<USER>/my_video-9.mkv', 'cap': None, 'points': []}
#}

# videos = {
# 'A': {'path': gst_pipeline1, 'cap': None, 'points': []},
# 'B': {'path': gst_pipeline2, 'cap': None, 'points': []}
# }

videos = {
    'A': {'path': '/home/<USER>/Downloads/my_video-9.mkv', 'cap': None, 'points': []},
    'B': {'path': '/home/<USER>/Downloads/my_video-11.mkv', 'cap': None, 'points': []}
}

video_states = {
    'A': {
        'mouse_active': False,
        'mouse_mode': 'y',
        'mouse_pressed': False,
        'box_start': (0, 0),
        'box_end': (0, 0),
        'corner_points': [],
        'boxes2': [],
        'cropping_mode': False,
        'cropping_area': (0, 0, 0, 0),
        'vehicles_entered': 0,
        'vehicles_leaved': 0,
        'tracked_ids': set(),
        'id_entered': set(),  # Set untuk menyimpan ID yang masuk
        'id_leaved': set(),   # Set untuk menyimpan ID yang keluar
        'cars_boxes': [],
        'available_slot' : 0,
        'parking_data': [],
        'id_obj' : set(),
        'vehicle_boxes' : [],
        'tracker' : Tracker(),
        'current_id_dict' : {},
        'obj' : [],
        'prev_time': time.time()
    },
    'B': {
        'mouse_active': False,
        'mouse_mode': 'y',
        'mouse_pressed': False,
        'box_start': (0, 0),
        'box_end': (0, 0),
        'corner_points': [],
        'boxes2': [],
        'cropping_mode': False,
        'cropping_area': (0, 0, 0, 0),
        'vehicles_entered': 0,
        'vehicles_leaved': 0,
        'tracked_ids': set(),
        'id_entered': set(),  # Set untuk menyimpan ID yang masuk
        'id_leaved': set(),   # Set untuk menyimpan ID yang keluar
        'cars_boxes': [],
        'available_slot' : 0,
        'parking_data': [],
        'id_obj' : set(),
        'vehicle_boxes' : [],
        'tracker' : Tracker(),
        'current_id_dict' : {},
        'obj' : [],
        'prev_time': time.time()
    }
}

video_locks = {video_id: threading.Lock() for video_id in videos}

input_slot = 0
mouse_pressed = False
prev_mouse_pressed = False
box_start = (0, 0)
box_end = (0, 0)
boxes2 = []
mouse_active = False
corner_points = []  
mouse_mode = "y"  
vehicles_entered = 0
vehicles_leaved = 0
tracked_ids = set()
cars_boxes = []
cy1 = 184
cy2 = 209
offset = 8
loop_time = 0
id_age_dict = {}
cropping_area = (0,0,0,0)
cropping_mode = False
crop_start = (0, 0)
crop_end = (0, 0)
count = 0
car_count = 0
bus_count = 0
truck_count = 0
cy1 = 184
cy2 = 209
offset = 8
loop_time = 0
id_age_dict = {}
cars_boxes = []

parking_stats = {
    'spots_available': 0,
    'vehicles_in': 0,
    'vehicles_out': 0
}

def change_status(state, target_id, status):
    for slot in state['parking_data']:
        if slot['id'] == target_id:
            slot['status'] = status
            break  

def clear_parking_area(video_id):
    if video_id in video_states:
        video_states[video_id]['parking_data'].clear()
    else:
        print(f"Area {video_id} tidak ditemukan.")

def refresh_parking_data(video_id, total):
    if video_id in video_states:
        video_states[video_id]['parking_data'] = []
        for i in range(1, total + 1):
            new_id = f"{video_id}{i}"
            video_states[video_id]['parking_data'].append({'id': new_id, 'status': 'Tersedia'})
    else:
        print(f"Area {video_id} tidak ditemukan.")

def update_parking_data(video_id, change):
    state = video_states[video_id]
    if change > 0:
        if state['parking_data']:
            last_id = state['parking_data'][-1]['id']
            last_number = int(last_id)
        else:
            last_number = 0
        
        for i in range(1, change + 1):
            new_id = f"{last_number + i}"
            state['parking_data'].append({'id': new_id, 'status': 'Tersedia'})

    elif change < 0:
        to_remove = min(abs(change), len(state['parking_data']))
        state['parking_data'] = state['parking_data'][:-to_remove]

def get_available_spot(video_id):
    if video_id not in video_states:
        print(f"Area {video_states} tidak ditemukan.")
        return 0

    state = video_states[video_id]
    return sum(1 for spot in state['parking_data'] if spot['status'] == 'Tersedia')

def is_point_in_box(px, py, box_coords):
    x1, y1, x2, y2, x3, y3, x4, y4 = box_coords
    def cross_product(x1, y1, x2, y2, x3, y3):
        return (x2 - x1) * (y3 - y1) - (y2 - y1) * (x3 - x1)

    d1 = cross_product(x1, y1, x2, y2, px, py)
    d2 = cross_product(x2, y2, x3, y3, px, py)
    d3 = cross_product(x3, y3, x4, y4, px, py)
    d4 = cross_product(x4, y4, x1, y1, px, py)

    # If the signs of all cross products are the same, the point is inside
    return (d1 >= 0 and d2 >= 0 and d3 >= 0 and d4 >= 0) or (d1 <= 0 and d2 <= 0 and d3 <= 0 and d4 <= 0)

def sort_corner_points(points):
    points_by_y = sorted(points, key=lambda p: p[1])
    top_points = points_by_y[:2]
    bottom_points = points_by_y[2:]

    top_points.sort(key=lambda p: p[0])
    bottom_points.sort(key=lambda p: p[0], reverse=True)
    return [top_points[0], top_points[1], bottom_points[0], bottom_points[1]]

def save_box2(video_id, input_slot):
    state = video_states[video_id]
    if len(state['corner_points']) == 4:
        # Mengurutkan corner points sebelum memproses
        sorted_corners = sort_corner_points(state['corner_points'])
        filename = f"data2_{video_id}.ul"
        if not os.path.exists(filename):
            # Jika file tidak ada, buat baru
            with open(filename, "w") as file:
                for x in range(input_slot):
                    x1 = sorted_corners[0][0] + (sorted_corners[1][0] - sorted_corners[0][0]) // input_slot * x
                    x2 = sorted_corners[0][0] + (sorted_corners[1][0] - sorted_corners[0][0]) // input_slot * (x + 1)
                    x3 = sorted_corners[3][0] + (sorted_corners[2][0] - sorted_corners[3][0]) // input_slot * (x + 1)
                    x4 = sorted_corners[3][0] + (sorted_corners[2][0] - sorted_corners[3][0]) // input_slot * x
                    y1 = sorted_corners[0][1] + (sorted_corners[1][1] - sorted_corners[0][1]) // input_slot * x
                    y2 = sorted_corners[0][1] + (sorted_corners[1][1] - sorted_corners[0][1]) // input_slot * (x + 1)
                    y3 = sorted_corners[3][1] + (sorted_corners[2][1] - sorted_corners[3][1]) // input_slot * (x + 1)
                    y4 = sorted_corners[3][1] + (sorted_corners[2][1] - sorted_corners[3][1]) // input_slot * x
                    file.write(f"{x1},{y1},{x2},{y2},{x3},{y3},{x4},{y4}\n")
        else:
            # Jika file sudah ada, tambahkan data baru
            with open(filename, "a") as file:
                # print(f"user : found ...")
                for x in range(input_slot):
                    x1 = sorted_corners[0][0] + (sorted_corners[1][0] - sorted_corners[0][0]) // input_slot * x
                    x2 = sorted_corners[0][0] + (sorted_corners[1][0] - sorted_corners[0][0]) // input_slot * (x + 1)
                    x3 = sorted_corners[3][0] + (sorted_corners[2][0] - sorted_corners[3][0]) // input_slot * (x + 1)
                    x4 = sorted_corners[3][0] + (sorted_corners[2][0] - sorted_corners[3][0]) // input_slot * x
                    y1 = sorted_corners[0][1] + (sorted_corners[1][1] - sorted_corners[0][1]) // input_slot * x
                    y2 = sorted_corners[0][1] + (sorted_corners[1][1] - sorted_corners[0][1]) // input_slot * (x + 1)
                    y3 = sorted_corners[3][1] + (sorted_corners[2][1] - sorted_corners[3][1]) // input_slot * (x + 1)
                    y4 = sorted_corners[3][1] + (sorted_corners[2][1] - sorted_corners[3][1]) // input_slot * x
                    file.write(f"{x1},{y1},{x2},{y2},{x3},{y3},{x4},{y4}\n")
    else:
        filename = f"data2_{video_id}.ul"
        if not os.path.exists(filename):
            default_box = (100, 100, 200, 100, 200, 200, 100, 200)  # Default coordinates for one parking slot
            with open(filename, "w") as file:
                file.write(f"{','.join(map(str, default_box))}")

def save_cropped_size(video_id):
    state = video_states[video_id]
    x1 = min(state['box_start'][0], state['box_end'][0])
    y1 = min(state['box_start'][1], state['box_end'][1])
    x2 = max(state['box_start'][0], state['box_end'][0])
    y2 = max(state['box_start'][1], state['box_end'][1])
    if ((x2-x1) ** 2 + (y2-y1) ** 2) ** 0.5 < 100:
        x2 = x1 + 100
        y2 = y1 + 100
        
    state['cropping_area'] = (x1, y1, x2, y2)
    
    filename = f"crop_{video_id}.ul"
    with open(filename, "w") as file:
        file.write(f"{x1},{y1},{x2},{y2}")
    
def is_obj_in_box(video_id, square_data):
    for bbox in video_states[video_id]['vehicle_boxes']:
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]

        # Calculate object center
        center_x = bbox[0] + width // 2
        center_y = bbox[1] + height // 2

        # Get frame width
        frame_width = frame_config['original_width']
        tolerencex = width//3
        tolerancey = height//8
        gapy = height//2

        # Determine check points based on object center position
        if center_x < frame_width // 3:
            # Object center is in left third - check points from center to bottom-left corner
            gapx = center_x-(bbox[0]+tolerencex)
            dstx = bbox[0]+tolerencex
            check_points = [
                (center_x, center_y),                           # Object center
                (dstx+gapx//3, center_y+gapy//3),                            # Left edge at center height
                (dstx+gapx*2//3, center_y+gapy*2//3),                            # Bottom edge at center width
                (dstx, bbox[3])                              # Bottom-left corner
            ]
        elif center_x > 2 * frame_width // 3:
            # Object center is in right third - check points from center to bottom-right corner
            gapx = bbox[1]-(center_x+tolerencex)
            dstx = bbox[2]-tolerencex
            check_points = [
                (center_x, center_y),                           # Object center
                (dstx-2*gapx//3, center_y+gapy//3),                            # Right edge at center height
                (dstx-gapx//3, center_y+gapy*2//3),                            # Bottom edge at center width
                (dstx, bbox[3]-tolerancey)                              # Bottom-right corner
            ]
        else:
            # Object center is in middle third - check points from center to bottom-center
            check_points = [
                (center_x, center_y),                           # Object center
                (center_x, center_y+gapy//3),               # Bottom-left quarter
                (center_x, center_y+gapy*2//3),                            # Bottom center
                (center_x, bbox[3]-tolerancey)                # Bottom-right quarter
            ]

        for point in check_points:
            if is_point_in_box(point[0], point[1], square_data):
                return True

    return False

# model = YOLO('../yolov8n_ncnn_model')
model = YOLO('yolov8n.pt')
with open("coco.txt", "r") as my_file:
    class_list = my_file.read().split("\n")


def init_video(video_id):
    if videos[video_id]['cap'] is None:
        
        videos[video_id]['cap'] = cv2.VideoCapture(videos[video_id]['path'])       
        # videos[video_id]['cap'] = cv2.VideoCapture(videos[video_id]['path'], cv2.CAP_GSTREAMER)
        
        cap = videos[video_id]['cap']
        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        frame_config['width'] = actual_width
        frame_config['height'] = actual_height

def resize_frame(frame):
    """Resize frame ke ukuran display yang ditentukan"""
    return cv2.resize(frame, (DISPLAY_WIDTH, DISPLAY_HEIGHT))

def scale_coordinates(x, y, from_width, from_height, to_width, to_height):
    """Scale koordinat dari satu resolusi ke resolusi lain"""
    scale_x = to_width / from_width
    scale_y = to_height / from_height
    
    scaled_x = int(x * scale_x)
    scaled_y = int(y * scale_y)
    
    return scaled_x, scaled_y

def init_box2(video_id):
    state = video_states[video_id]
    state['boxes2'] = []
    state['parking_data'] = []
    filename = f"data2_{video_id}.ul"   
    
    if os.path.exists(filename):
        with open(filename, "r") as file:
            for line in file:
                x1, y1, x2, y2, x3, y3, x4, y4 = map(int, line.strip().split(","))
                state['boxes2'].append((x1, y1, x2, y2, x3, y3, x4, y4))
    else:
        # Create default parking area if file doesn't exist
        default_box = (100, 100, 200, 100, 200, 200, 100, 200)  # Default coordinates for one parking slot
        state['boxes2'].append(default_box)
        with open(filename, "w") as file:
            file.write(f"{','.join(map(str, default_box))}\n")
    refresh_parking_data(video_id, len(state['boxes2']));

def init_crop_area(video_id):
    state = video_states[video_id]
    filename = f"crop_{video_id}.ul"
    
    if os.path.exists(filename):
        state['cropping_mode'] = True
        with open(filename, "r") as file:
            for line in file:
                x1, y1, x2, y2 = map(int, line.strip().split(","))
                state['cropping_area'] = (x1, y1, x2, y2)
    else:
        # Create default crop area if file doesn't exist
        default_crop = (50, 50, 550, 450)  # Default coordinates for crop area
        state['cropping_area'] = default_crop
        state['cropping_mode'] = True
        with open(filename, "w") as file:
            file.write(f"{','.join(map(str, default_crop))}")


def process_frame(state, frame):
    # Tambahkan variabel untuk FPS
    # current_time = time.time()
    # fps = 1.0 / (current_time - state.get('prev_time', current_time))
    # state['prev_time'] = current_time
    
    # Proses frame seperti biasa
    if state['cropping_mode']:
        x1_crop, y1_crop, x2_crop, y2_crop = state['cropping_area']
        cropped_frame = frame[y1_crop:y2_crop, x1_crop:x2_crop]
        if cropped_frame is None or cropped_frame.size == 0:
            print("Warning: Cropped frame is empty. Skipping inference...")
            return frame
        else:
            results = model.predict(cropped_frame)
    else:
        results = model.predict(frame)

    # cv2.putText(frame, f"FPS: {int(fps)}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 
    #             1, (0, 255, 0), 2)

    return frame

def periodic_frame_processing(video_id, interval=2):  # interval dalam detik
    global cars_boxes, vehicles_entered, vehicles_leaved, tracked_ids, car_count, confident_treshold
    init_box2(video_id)  
    init_crop_area(video_id)
    state = video_states[video_id]
    while True:
        with video_locks[video_id]:
            init_video(video_id)
            cap = videos[video_id]['cap']
            success, frame = cap.read()
            if not success:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue

            if frame_config['original_width'] != int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)) or \
               frame_config['original_height'] != int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)):
                frame_config['original_width'] = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                frame_config['original_height'] = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            if state['cropping_mode']:
                x1_crop, y1_crop, x2_crop, y2_crop = state['cropping_area']
                cropped_frame = frame[y1_crop:y2_crop, x1_crop:x2_crop]
                if cropped_frame is None or cropped_frame.size == 0:
                    continue
            else:
                cropped_frame = frame

            results = model.predict(cropped_frame)
            detections = results[0].boxes.data
            px = pd.DataFrame(detections).astype("float")

            state['obj'] = []
            for index, row in px.iterrows():
                x1 = int(state['cropping_area'][0] + row[0])
                y1 = int(state['cropping_area'][1] + row[1])
                x2 = int(state['cropping_area'][0] + row[2])
                y2 = int(state['cropping_area'][1] + row[3])
                d = int(row[5])
                confidence = float(row[4])
                c = class_list[d]
                if confidence > confident_treshold:
                    if 'car' in c or 'bus' in c or 'truck' in c:
                        state['obj'].append([x1, y1, x2, y2, confidence])

            state['vehicle_boxes'] = state['tracker'].update(state['obj'])

            for bbox in state['vehicle_boxes']:
                cx = int((bbox[0] + bbox[2]) / 2)
                cy = int((bbox[1] + bbox[3]) / 2)
                if (cy > cy1 - offset) and (cy < cy1 + offset):
                    car_count += 1

            for bbox in state['vehicle_boxes']:
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 255), 2)
                cvzone.putTextRect(frame, f'{bbox[5]}, {bbox[4]:.2f}', (bbox[0], bbox[1]), 1, 1)

            index = 0
            prev = False
            for park_slot in state['boxes2']:
                if is_obj_in_box(video_id, park_slot):
                    change_status(state, f"{video_id}{index + 1}", "Terisi")
                    cv2.line(frame, (park_slot[0], park_slot[1]), (park_slot[6], park_slot[7]), (255, 0, 0), 3)
                    for x in range(0, 5, 2):
                        cv2.line(frame, (park_slot[x], park_slot[x+1]), (park_slot[x+2], park_slot[x+3]), (255, 0, 0), 3)
                else:
                    change_status(state, f"{video_id}{index + 1}", "Tersedia")
                    cv2.line(frame, (park_slot[0], park_slot[1]), (park_slot[6], park_slot[7]), (0, 0, 255), 3)
                    for x in range(0, 5, 2):
                        cv2.line(frame, (park_slot[x], park_slot[x+1]), (park_slot[x+2], park_slot[x+3]), (0, 0, 255), 3)
                
                # Ubah cara mengakses parking_data
                if index < len(state['parking_data']):
                    slot_id = f"{video_id}{index + 1}"  # Membuat ID seperti A1, A2, dst
                    cv2.putText(frame, slot_id, 
                              (park_slot[6] + (park_slot[4] - park_slot[6]) // 2, park_slot[7]), 
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2, cv2.LINE_AA)
                index += 1

            for point in state['corner_points']:
                cv2.circle(frame, point, 5, (0, 0, 255), -1)

            state['id_obj'] = set((obj[5], obj[6]) for obj in state['vehicle_boxes'])
            current_id_only = set(obj[5] for obj in state['vehicle_boxes'])  # Set yang hanya berisi ID
            
            # Masuk kendaraan baru
            for obj_id, age_obj in state['id_obj']:
                if obj_id not in state['id_entered'] and age_obj > 5:
                    state['id_entered'].add(obj_id)  # Tambahkan ke set ID yang masuk
                    state['vehicles_entered'] = len(state['id_entered'])  # Update jumlah kendaraan masuk
                    print(f"ID {obj_id} entered in {video_id}. Total vehicles entered: {state['vehicles_entered']}")
            
            # Kendaraan yang keluar - hanya jika ID benar-benar tidak ada lagi
            for obj_id in list(state['id_entered']):
                if obj_id not in current_id_only:  # ID tidak ada di frame saat ini
                    if obj_id not in state['id_leaved']:  # dan belum tercatat sebagai keluar
                        state['id_leaved'].add(obj_id)  # Tambahkan ke set ID yang keluar
                        state['vehicles_leaved'] = len(state['id_leaved'])  # Update jumlah kendaraan keluar
                        print(f"ID {obj_id} leaved from {video_id}. Total vehicles leaved: {state['vehicles_leaved']}")
            
            # Jika ID yang sudah tercatat keluar muncul lagi, hapus dari daftar keluar
            for obj_id in list(state['id_leaved']):
                if obj_id in current_id_only:
                    state['id_leaved'].remove(obj_id)
                    state['vehicles_leaved'] = len(state['id_leaved'])  # Update jumlah kendaraan keluar
                    print(f"ID {obj_id} returned to {video_id}. Total vehicles leaved: {state['vehicles_leaved']}")
            
            cv2.putText(frame, f"Masuk: {state['vehicles_entered']}", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Keluar: {state['vehicles_leaved']}", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

            for point in videos[video_id]['points']:
                x, y = point['x'], point['y']
                cv2.circle(frame, (x, y), 5, (0, 0, 255), -1)

            processed_frame = process_frame(state, frame)
            state['last_frame'] = resize_frame(processed_frame)

        time.sleep(interval)

def generate_frames(video_id):
    while True:
        with video_locks[video_id]:
            frame = video_states[video_id].get('last_frame', None)
            if frame is None:
                continue

            ret, buffer = cv2.imencode('.jpg', frame)
            frame = buffer.tobytes()

            video_states[video_id]['available_slot'] = get_available_spot(video_id)

        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
        time.sleep(1)

@app.route('/')
def index():
    return render_template('index.html', 
                         frame_width=frame_config['display_width'],
                         frame_height=frame_config['display_height'],
                         session=session)

@app.route('/video_feed/<video_id>')
def video_feed(video_id):
    print(f"[INFO] /video_feed/{video_id} dipanggil")
    return Response(generate_frames(video_id), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/mark_point/<video_id>', methods=['POST'])
def mark_point(video_id):
    data = request.get_json()
    x = int(data['x'])
    y = int(data['y'])
    with video_locks[video_id]:
        videos[video_id]['points'].append({'x': x, 'y': y})
    return jsonify({'message': 'Point marked successfully'})

@app.route('/video_dimensions')
def video_dimensions():
    return jsonify(frame_config)

@app.route('/mouse_event', methods=['POST'])
def handle_mouse_event():
    """Handle mouse events from web interface"""
    try:
        data = request.get_json()
        video_id = data.get('videoId')
        event_type = data.get('event')
        mouse_mode = data.get('mouse_mode', 'y')  # Default to 'y' if not provided
        
        if video_id not in video_states:
            return jsonify({'success': False, 'message': 'Invalid video ID'}), 400
        
        state = video_states[video_id]
        
        # Update mouse mode from request
        state['mouse_mode'] = mouse_mode
        
        if not state['mouse_active'] and mouse_mode != 'y':
            state['mouse_active'] = True
        
        if mouse_mode == 'y':
            state['mouse_active'] = False
            return jsonify({'success': True, 'message': 'Mouse not active'})

        display_x = int(data.get('x'))
        display_y = int(data.get('y'))
        
        x, y = scale_coordinates(
            display_x, display_y,
            frame_config['display_width'], frame_config['display_height'],
            frame_config['original_width'], frame_config['original_height']
        )
        
        response_data = {
            'success': True,
            'refresh': False,
            'message': ''
        }
        
        if state['mouse_mode'] == "o":
            if event_type == 'click':
                state['corner_points'].append((x, y))
                response_data['message'] = f'Point added: {len(state["corner_points"])}/4'
                
                if len(state['corner_points']) == 4:
                    response_data['refresh'] = True
                    response_data['action'] = 'request_slot_input'
                    response_data['videoId'] = video_id
                
                elif len(state['corner_points']) > 4:
                    state['corner_points'].pop(0)
                    
        elif state['mouse_mode'] == 'y':
            if video_states['A']['corner_points']:
                video_states['A']['corner_points'].pop(0)
            if video_states['B']['corner_points']:
                video_states['B']['corner_points'].pop(0)
            
        elif state['mouse_mode'] == "d":
            if video_states['A']['corner_points']:
                video_states['A']['corner_points'].pop(0)
            if video_states['B']['corner_points']:
                video_states['B']['corner_points'].pop(0)
            if event_type == 'click':
                for park_slot in state['boxes2']:
                    if is_point_in_box(x, y, park_slot):
                        state['boxes2'].remove(park_slot)
                        filename = f"data2_{video_id}.ul"
                        with open(filename, "w") as file:
                            for box in state['boxes2']:
                                file.write(f"{','.join(map(str, box))}\n")
                        refresh_parking_data(video_id, len(state['boxes2']))
                        response_data['refresh'] = True
                        response_data['message'] = 'Parking slot deleted'
                        break
                
        elif state['mouse_mode'] == "c":
            if video_states['A']['corner_points']:
                video_states['A']['corner_points'].pop(0)
            if video_states['B']['corner_points']:
                video_states['B']['corner_points'].pop(0)
            if event_type == 'mousedown':
                state['mouse_pressed'] = True
                state['box_start'] = (x, y)
                response_data['message'] = 'Started cropping'
                
            elif event_type == 'mousemove' and state['mouse_pressed']:
                state['box_end'] = (x, y)
                display_start_x, display_start_y = scale_coordinates(
                    state['box_start'][0], state['box_start'][1],
                    frame_config['original_width'], frame_config['original_height'],
                    frame_config['display_width'], frame_config['display_height']
                )
                display_end_x, display_end_y = scale_coordinates(
                    state['box_end'][0], state['box_end'][1],
                    frame_config['original_width'], frame_config['original_height'],
                    frame_config['display_width'], frame_config['display_height']
                )
                response_data['box'] = {
                    'start': (display_start_x, display_start_y),
                    'end': (display_end_x, display_end_y)
                }
                
            elif event_type == 'mouseup' or event_type == 'click':
                state['mouse_pressed'] = False
                state['box_end'] = (x, y)
                save_cropped_size(video_id) 
                init_crop_area(video_id)
                state['cropping_mode'] = True
                response_data['refresh'] = True
                response_data['message'] = 'Crop area saved'

        print(f"mouse_mode: {state['mouse_mode']}, data A : {video_states['A']['corner_points']} data B : {video_states['B']['corner_points']}")
        return jsonify(response_data)
    except Exception as e:
        app.logger.error(f"Error in mouse_event: {str(e)}")
        return jsonify({'success': False, 'message': f'Server error: {str(e)}'}), 500


@app.route('/set_slot_input', methods=['POST'])
def set_slot_input():
    """Handle slot input from web interface"""
    data = request.get_json()
    video_id = data.get('videoId')
    input_slot = int(data.get('slots', 1))
    
    if video_id not in video_states:
        return jsonify({'success': False, 'message': 'Invalid video ID'}), 400
        
    state = video_states[video_id]
    save_box2(video_id, input_slot) 
    init_box2(video_id)  
    state['corner_points'].clear()
    
    return jsonify({
        'success': True,
        'message': f'Created {input_slot} parking slots for {video_id}'
    })

@app.route('/set_mouse_mode', methods=['POST'])
def set_mouse_mode():
    data = request.get_json()
    video_id = data.get('videoId')
    mode = data.get('mode')
    
    if video_id not in video_states:
        return jsonify({'success': False, 'message': 'Invalid video ID'}), 400
    if mode in ['c', 'o', 'd']:
        video_states[video_id]['mouse_mode'] = mode
        video_states[video_id]['mouse_active'] = True
        return jsonify({
            'success': True, 
            'message': f"Mouse mode set to {mode} for {video_id}"
        })
    elif mode == 'y':
        video_states[video_id]['mouse_mode'] = mode
        video_states[video_id]['mouse_active'] = False
        return jsonify({
            'success': True, 
            'message': f"Mouse mode disabled for {video_id}"
        })
    else:
        return jsonify({'success': False, 'message': 'Invalid mode'}), 400
    
@app.route('/get_stats')
def get_stats():
    stats = {
        'A': {
            'available_slot': video_states['A']['available_slot'],
            'vehicles_entered': video_states['A']['vehicles_entered'],
            'vehicles_leaved': video_states['A']['vehicles_leaved']
        },
        'B': {
            'available_slot': video_states['B']['available_slot'],
            'vehicles_entered': video_states['B']['vehicles_entered'],
            'vehicles_leaved': video_states['B']['vehicles_leaved']
        }
    }
    return jsonify(stats)

@app.route("/api/parking/<video_id>")
def api_parking(video_id):
    data = video_states[video_id]['parking_data']
    return jsonify({
        "spots": data
    })

@app.route("/api/parking/delete", methods=['POST'])
def api_parking_delete():
    data = request.get_json()
    spot_id = data.get('spotId')
   
    video_id = spot_id[0]
    index_id = int(spot_id[1:]) 
    
    if video_id not in video_states:
        return jsonify({'success': False, 'message': 'Invalid video ID'}), 400
    
    state = video_states[video_id]
    
    state['parking_data'] = [spot for spot in state['parking_data'] if spot['id'] != spot_id]
    
    if 0 <= index_id - 1 < len(state['boxes2']):
        del state['boxes2'][index_id - 1]
        
        filename = f"data2_{video_id}.ul"
        with open(filename, "w") as file:
            for box in state['boxes2']:
                file.write(f"{','.join(map(str, box))}\n")
        
        refresh_parking_data(video_id, len(state['boxes2']))
        
        return jsonify({'success': True, 'message': f'Spot {spot_id} deleted'})
    else:
        return jsonify({'success': False, 'message': f'Invalid index {index_id} for video {video_id}'}), 400

if __name__ == '__main__':
    # app.run(debug=True, host='0.0.0.0', port=5000)
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        for vid in videos:
            threading.Thread(target=periodic_frame_processing, args=(vid,), daemon=True).start()
    # app.run(debug=False)
    app.run(debug=True, host='0.0.0.0', port=5000)
