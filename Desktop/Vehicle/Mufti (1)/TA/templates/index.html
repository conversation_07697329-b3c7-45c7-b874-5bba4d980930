<!DOCTYPE html>
<html>
<head>
    <title>Gedung AD Cam</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Add Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=0.55">
    <style>
        :root {
            --frame-width: {{ frame_width }}px;
            --frame-height: {{ frame_height }}px;
        }
        
        .video-wrapper {
            position: relative;
        }

        .video-container {
            position: relative;
            /* width: var(--frame-width); */
            width: 100%;
            max-width: var(--frame-width);
            height: var(--frame-height);
            overflow: hidden;
        }

        .video-container img,
        .video-container canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .video-container canvas {
            z-index: 1;
            pointer-events: all;  /* Penting! */
            cursor: crosshair;
        }

        .control-buttons {
            text-align: center;
        }

        .control-buttons button {
            transition: all 0.3s ease;
        }

        .control-buttons button:hover {
            opacity: 0.9;
        }

        .control-buttons button.active {
            transform: scale(0.95);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* Style untuk link admin */
        .admin-link {
            position: fixed;
            top: 15px;
            right: 15px;
            z-index: 1000;
        }
        
        /* Style untuk modal login */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            width: 300px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        /* Hide control buttons by default (guest mode) */
        .control-buttons {
            display: none;
        }
        
        /* Show control buttons when in admin mode */
        body.admin-mode .control-buttons {
            display: block;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .header {
            background-color: #ffffff;
            padding: 1rem 1.5rem;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            position: relative;
            z-index: 10;
        }
        .header-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
        }
        .admin-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .admin-button:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body class="{{ 'admin-mode' if session.get('is_admin') else '' }}">
    <!-- Login Modal -->
    <div class="container mx-auto">
        <div id="loginModal" class="modal">
            <div class="modal-content">
                <h2 class="text-xl font-bold mb-4">Admin Login</h2>
                <div id="loginError" class="text-red-500 text-sm mb-4 hidden">Invalid username or password</div>
                <form id="loginForm">
                    <div class="mb-4">
                        <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                        <input type="text" id="username" name="username" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="mb-4">
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" id="password" name="password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex justify-end">
                        <button type="button" onclick="closeLoginModal(document.getElementById('loginModal'), document.getElementById('loginError'), document.getElementById('loginForm'))" class="mr-2 px-4 py-2 bg-gray-200 text-gray-800 rounded">Cancel</button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded">Login</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="flex flex-col w-full">
            <!-- Header with Admin Link -->
            <header class="header">
                <div class="flex items-center gap-3">
                <span class="material-icons text-3xl text-blue-500">videocam</span>
                <h1 class="header-title">Gedung AD</h1>
                </div>
                <button id="authLink" class="admin-button">
                <span class="material-icons">admin_panel_settings</span>
                🔑 {{ "Login as Guest" if session.get('is_admin') else "Admin Login" }}
                </button>
            </header>

            <div class="max-w-[1600px] mx-auto mt-8">
                <div class="grid w-full grid-cols-1 gap-6 mb-6 md:grid-cols-1 lg:grid-cols-1 xl:grid-cols-2 2xl:gap-12 justify-items-center">
                    <!-- Video Stream A -->
                    <div class="video-wrapper">
                        <div class="video-container w-full aspect-video bg-black rounded-lg shadow-md">
                            <img id="video_streamA" src="/video_feed/A" 
                                width="{{ frame_width }}" 
                                height="{{ frame_height }}" />
                            <canvas id="overlay-canvasA" 
                                width="{{ frame_width }}" 
                                height="{{ frame_height }}">
                            </canvas>
                        </div>
                        <div class="flex gap-4 mt-4 justify-center">
                            <div id="spotsA" class="px-4 py-2 bg-gray-500 text-white rounded">Spots Tersedia: 0</div>
                            <div id="inA" class="px-4 py-2 bg-gray-500 text-white rounded">Kendaraan Masuk: 0</div>
                            <div id="outA" class="px-4 py-2 bg-gray-500 text-white rounded">Kendaraan Keluar: 0</div>
                        </div>
                    </div>

                    <!-- Video Stream B -->
                    <div class="video-wrapper">
                        <div class="video-container w-full aspect-video bg-black rounded-lg shadow-md">
                            <img id="video_streamB" src="/video_feed/B" 
                                width="{{ frame_width }}" 
                                height="{{ frame_height }}" />
                            <canvas id="overlay-canvasB" 
                                width="{{ frame_width }}" 
                                height="{{ frame_height }}">
                            </canvas>
                        </div>
                        <div class="flex gap-4 mt-4 justify-center">
                            <div id="spotsB" class="px-4 py-2 bg-gray-500 text-white rounded">Spots Tersedia: 0</div>
                            <div id="inB" class="px-4 py-2 bg-gray-500 text-white rounded">Kendaraan Masuk: 0</div>
                            <div id="outB" class="px-4 py-2 bg-gray-500 text-white rounded">Kendaraan Keluar: 0</div>
                        </div>
                    </div>
                </div>

                <div class="control-buttons mt-4">
                    <button id="selectBtn" onclick="toggleSelect()" class="px-4 py-2 bg-blue-500 text-white rounded mr-2">Select Area</button>
                    <button id="AddBtn" onclick="toggleAddSlot()" class="px-4 py-2 bg-green-500 text-white rounded">Add Slot</button>
                    <button id="DeleteBtn" onclick="toggleDeleteSlot()" class="px-4 py-2 bg-green-500 text-white rounded">Delete Slot</button>
                </div>

                <!-- Parking sections -->
                <section class="mb-8" id="section-a">
                <h2 class="text-center text-lg mb-4 font-normal">Gedung AD-A</h2>
                <div class="flex flex-wrap justify-center gap-x-10 gap-y-6 max-w-[80%] mx-auto" id="spots-a">
                    <!-- Spots A will be dynamically rendered by Flask -->
                </div>
                </section>
            
                <section class="mb-8" id="section-b">
                <h2 class="text-center text-lg mb-4 font-normal">Gedung AD-B</h2>
                <div class="flex flex-wrap justify-center gap-x-10 gap-y-6 max-w-[80%] mx-auto" id="spots-b">
                    <!-- Spots B will be dynamically rendered by Flask -->
                </div>
                </section>
            </div>
        </div>
    </div>
    <!-- Pass template variables to JavaScript -->
    <script>
        window.FRAME_WIDTH = {{ frame_width }};
        window.FRAME_HEIGHT = {{ frame_height }};
        window.IS_ADMIN = {{ 'true' if session.get('is_admin') else 'false' }};
    </script>
    
    <!-- Load main.js -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
