import cv2

gst_pipeline1 = (
    "v4l2src device=/dev/video0 ! "
    "image/jpeg, width=1280, height=720, framerate=30/1 ! "
    "jpegdec ! videoconvert ! appsink"
)

gst_pipeline2 = (
    "v4l2src device=/dev/video2 ! "
    "image/jpeg, width=1280, height=720, framerate=30/1 ! "
    "jpegdec ! videoconvert ! appsink"
)


cap1 = cv2.VideoCapture(gst_pipeline1, cv2.CAP_GSTREAMER)
cap2 = cv2.VideoCapture(gst_pipeline2, cv2.CAP_GSTREAMER)

if not cap2.isOpened():
    print("kamera 2 tidak dapat dibuka!")
    exit()
elif not cap1.isOpened():
    print("kamera 1 tidak dapat dibuka!")
    exit()

while True:
    ret1, frame1 = cap1.read()
    ret2, frame2 = cap2.read()

    if not ret1 or not ret2:
        print("Gagal membaca frame dari salah satu kamera.")
        break

    # Tampilkan kedua frame
    cv2.imshow('Camera 1', frame1)
    cv2.imshow('Camera 2', frame2)

    key = cv2.waitKey(1)
    if key == 27:  # ESC
        break

cap1.release()
cap2.release()
cv2.destroyAllWindows()
