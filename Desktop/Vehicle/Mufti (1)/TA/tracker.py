import math


class Tracker:
    def __init__(self):
        # Store the center positions of the objects
        self.center_points = {}
        # Keep the count of the IDs
        self.id_count = 0
        # Dictionary to store object ages
        self.object_ages = {}
        # Dictionary to track disappearance counts
        self.disappearance_counts = {}
        # Dictionary to track duplicate IDs
        self.duplicate_ids = {}
        # Maximum cycles to keep duplicates
        self.max_duplicate_cycles = 5
        # Store object dimensions (width, height)
        self.object_dimensions = {}

    def update(self, objects_rect):
        # Objects boxes and ids
        objects_bbs_ids = []
        
        # Track current detected object IDs
        current_object_ids = set()
        # Track IDs that have been matched in this frame
        matched_ids = set()

        # Get center point of new object
        for rect in objects_rect:
            x1, y1, x2, y2, conv = rect
            cx = (x1 + x2) // 2
            cy = (y1 + y2) // 2

            # Find out if that object was detected already
            same_object_detected = False
            potential_matches = []
            
            # First pass: find all potential matches
            for object_id, pt in self.center_points.items():
                dist = math.hypot(cx - pt[0], cy - pt[1])
                
                # Calculate dynamic threshold based on the stored bounding box dimensions
                # Get the last known width and height (approximate from center)
                # We'll need to store width and height with center points
                if hasattr(self, 'object_dimensions') and object_id in self.object_dimensions:
                    w, h = self.object_dimensions[object_id]
                    threshold = math.sqrt(w**2 + h**2) / 3
                else:
                    threshold = 100  # Fallback to fixed threshold
                
                # Check if distance is within threshold and dimensions are similar (within 20% tolerance)
                if dist < threshold and object_id in self.object_dimensions:
                    stored_w, stored_h = self.object_dimensions[object_id]
                    current_w, current_h = x2 - x1, y2 - y1
                    
                    # Calculate dimension differences as percentages
                    w_diff_pct = abs(current_w - stored_w) / stored_w if stored_w > 0 else float('inf')
                    h_diff_pct = abs(current_h - stored_h) / stored_h if stored_h > 0 else float('inf')
                    
                    # Check if dimensions are within 20% tolerance
                    if w_diff_pct <= 0.2 and h_diff_pct <= 0.2:
                        potential_matches.append((object_id, dist))
                        # print(f"Potential match found between {object_id} and new object. x = {cx} y = {cy} Distance: {dist}, Width diff: {w_diff_pct}, Height diff: {h_diff_pct}")
                
                #?
                elif dist < threshold and object_id not in self.object_dimensions:
                    # For objects without stored dimensions yet, just use distance
                    potential_matches.append((object_id, dist))
            
            # Sort potential matches by distance (closest first)
            potential_matches.sort(key=lambda x: x[1])
            
            # If we have matches
            if potential_matches:
                # Get the closest match that hasn't been matched yet
                for object_id, _ in potential_matches:
                    if object_id not in matched_ids:
                        # Check if this object_id's center point is too close to any already matched objects
                        collision_detected = False
                        for matched_id in matched_ids:
                            # Skip checking collision with itself
                            if matched_id == object_id:
                                continue
                                
                            matched_center = self.center_points[matched_id]
                            # Use current detection position (cx, cy) instead of stored position (pt)
                            collision_dist = math.hypot(cx - matched_center[0], cy - matched_center[1])
                            # Use the same threshold logic as for matching
                            if hasattr(self, 'object_dimensions') and matched_id in self.object_dimensions:
                                matched_w, matched_h = self.object_dimensions[matched_id]
                                collision_threshold = math.sqrt(matched_w**2 + matched_h**2) / 4  # Stricter threshold
                            else:
                                collision_threshold = 75  # Stricter than regular matching
                            
                            if collision_dist < collision_threshold:
                                collision_detected = True
                                # print(f"Collision detected between {object_id} and {matched_id} threshold = {collision_threshold} distance = {collision_dist}")
                                break
                        
                        if not collision_detected:
                            # Update the center point of the detected object
                            self.center_points[object_id] = (cx, cy)
                            # Store object dimensions
                            width = x2 - x1
                            height = y2 - y1
                            self.object_dimensions[object_id] = (width, height)
                            # Increment age for existing object
                            self.object_ages[object_id] = self.object_ages.get(object_id, 0) + 1
                            # Reset disappearance count since object is detected
                            self.disappearance_counts[object_id] = 0
                            # Add box with ID and age
                            objects_bbs_ids.append([x1, y1, x2, y2, conv, object_id, self.object_ages[object_id]])
                            same_object_detected = True
                            current_object_ids.add(object_id)
                            matched_ids.add(object_id)
                            
                            # If this was a duplicate, remove it from duplicates
                            if object_id in self.duplicate_ids:
                                del self.duplicate_ids[object_id]
                            break
                
                # If we found a match but there are other potential matches,
                # mark them as duplicates to keep them around for a while
                if same_object_detected and len(potential_matches) > 1:
                    for object_id, _ in potential_matches[1:]:
                        if object_id not in matched_ids and object_id not in self.duplicate_ids:
                            self.duplicate_ids[object_id] = 0

            # If the object is not detected, assign a new ID
            if not same_object_detected:
                # Check if this object is very close to any existing object
                # This handles cases where an object is detected twice in the same frame
                duplicate_detection = False
                for existing_id in current_object_ids:
                    existing_center = self.center_points[existing_id]
                    dist = math.hypot(cx - existing_center[0], cy - existing_center[1])
                    
                    # Use dynamic threshold based on object dimensions
                    if existing_id in self.object_dimensions:
                        w, h = self.object_dimensions[existing_id]
                        duplicate_threshold = math.sqrt(w**2 + h**2) / 3
                    else:
                        duplicate_threshold = 100
                    
                    # If very close to existing object, consider it a duplicate detection
                    if dist < duplicate_threshold:
                        duplicate_detection = True
                        # print(f"Duplicate detection prevented: {cx},{cy} is too close to existing ID {existing_id}")
                        break
                
                # Only create new ID if not a duplicate detection
                if not duplicate_detection:
                    self.center_points[self.id_count] = (cx, cy)
                    # Initialize age for new object
                    self.object_ages[self.id_count] = 1
                    # Initialize disappearance count
                    self.disappearance_counts[self.id_count] = 0
                    # Add box with ID and initial age
                    objects_bbs_ids.append([x1, y1, x2, y2, conv, self.id_count, 1])
                    current_object_ids.add(self.id_count)
                    matched_ids.add(self.id_count)
                    self.id_count += 1
                    print(f"ID Created")

        # Handle objects that weren't detected in this frame
        for object_id in list(self.center_points.keys()):
            if object_id not in current_object_ids:
                # If it's a duplicate ID, increment its cycle count
                if object_id in self.duplicate_ids:
                    self.duplicate_ids[object_id] += 1
                    # If it's been a duplicate for too long, don't keep it
                    if self.duplicate_ids[object_id] > self.max_duplicate_cycles:
                        continue
                
                # If object age > 10, keep it for up to 10 cycles
                if self.object_ages[object_id] > 10:
                    self.disappearance_counts[object_id] = self.disappearance_counts.get(object_id, 0) + 1
                    
                    # If disappeared for less than 10 cycles, keep it
                    if self.disappearance_counts[object_id] <= 10:
                        # Use stored dimensions if available
                        if object_id in self.object_dimensions:
                            width, height = self.object_dimensions[object_id]
                            x1 = self.center_points[object_id][0] - width // 2
                            y1 = self.center_points[object_id][1] - height // 2
                            x2 = x1 + width
                            y2 = y1 + height
                        else:
                            # Fallback to approximate if dimensions not stored
                            x1 = self.center_points[object_id][0] - 50
                            y1 = self.center_points[object_id][1] - 50
                            x2 = x1 + 100
                            y2 = y1 + 100
                        
                        current_object_ids.add(object_id)

        # Clean the dictionaries by keeping only IDs that are still in the frame or being preserved
        new_center_points = {}
        new_object_ages = {}
        new_disappearance_counts = {}
        new_duplicate_ids = {}
        new_object_dimensions = {}
        
        # Keep objects that are either detected, being preserved, or are duplicates within cycle limit
        for object_id in list(self.center_points.keys()):
            keep_object = False
            
            # Keep if currently detected
            if object_id in current_object_ids:
                keep_object = True
            # Keep if it's a mature object (age > 10) that hasn't been gone too long
            elif self.object_ages.get(object_id, 0) > 4 and self.disappearance_counts.get(object_id, 0) <= 10:
                keep_object = True
            # Keep if it's a duplicate that hasn't exceeded its cycle limit
            elif object_id in self.duplicate_ids and self.duplicate_ids[object_id] <= self.max_duplicate_cycles:
                keep_object = True
                new_duplicate_ids[object_id] = self.duplicate_ids[object_id]
            
            if keep_object:
                new_center_points[object_id] = self.center_points[object_id]
                new_object_ages[object_id] = self.object_ages[object_id]
                new_disappearance_counts[object_id] = self.disappearance_counts.get(object_id, 0)
                if object_id in self.object_dimensions:
                    new_object_dimensions[object_id] = self.object_dimensions[object_id]

        self.center_points = new_center_points
        self.object_ages = new_object_ages
        self.disappearance_counts = new_disappearance_counts
        self.duplicate_ids = new_duplicate_ids
        self.object_dimensions = new_object_dimensions
        
        return objects_bbs_ids

    def reset(self):
        """Reset all tracking data."""
        self.center_points = {}
        self.id_count = 0
        self.object_ages = {}
        self.disappearance_counts = {}
        self.duplicate_ids = {}
        self.object_dimensions = {}
