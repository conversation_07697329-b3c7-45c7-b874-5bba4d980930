.container {
    max-width: 100%;
    padding: 20px;
    box-sizing: border-box;
}

.video-container {
    width: 100%;
    max-width: var(--frame-width);
    margin: 0 auto;
}

#video_stream {
    width: 100%;
    height: auto;
    border: 2px solid black;
    display: block;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
}

h1 {
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 24px;
    }
}

.control-buttons {
    margin: 20px auto;
    text-align: center;
    max-width: var(--frame-width);
}

.control-buttons button {
    padding: 10px 20px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background-color: #007bff;
    color: white;
    transition: background-color 0.3s;
}

.control-buttons button:hover {
    background-color: #0056b3;
}

.control-buttons button.active {
    background-color: #28a745;
}
