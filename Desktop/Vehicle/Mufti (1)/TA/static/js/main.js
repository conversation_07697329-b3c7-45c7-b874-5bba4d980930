
// Constants from template
const FRAME_WIDTH = window.FRAME_WIDTH;  // Will be set from template
const FRAME_HEIGHT = window.FRAME_HEIGHT;  // Will be set from template

// Global variables
let isSelectMode = false;
let isMarkMode = false;
let isDeleteMode = false;
let mousePressed = false;
let canvas, ctx;
let lastInteractedVideoId = 'A'; // Default to A
let isAdmin = window.IS_ADMIN || false;
let MouseMode = 'y';
const canvasEventHandlers = {};


// DOM elements
document.addEventListener('DOMContentLoaded', function() {
    const loginModal = document.getElementById('loginModal');
    const loginForm = document.getElementById('loginForm');
    const loginError = document.getElementById('loginError');
    const authLink = document.getElementById('authLink');
    
    // console.log("Auth elements:", { 
    //     loginModal: !!loginModal, 
    //     loginForm: !!loginForm, 
    //     loginError: !!loginError, 
    //     authLink: !!authLink 
    // });
    
    if (authLink) {
        authLink.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (isAdmin) {
                // Logout if already admin
                logout();
            } else {
                // Show login modal if guest
                if (loginModal) {
                    loginModal.style.display = 'flex';
                    // console.log("Login modal displayed");
                } 
                // else {
                //     console.error("Login modal not found");
                // }
            }
        });
    }
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            login(username, password, loginError, loginModal);
        });
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (loginModal && e.target === loginModal) {
            closeLoginModal(loginModal, loginError, loginForm);
        }
    });
});

/**
 * Close login modal
 */
function closeLoginModal(loginModal, loginError, loginForm) {
    if (loginModal) loginModal.style.display = 'none';
    if (loginError) loginError.classList.add('hidden');
    if (loginForm) loginForm.reset();
}

/**
 * Login function
 */
function login(username, password, loginError, loginModal) {
    fetch('/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            if (loginError) loginError.classList.remove('hidden');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (loginError) {
            loginError.classList.remove('hidden');
            loginError.textContent = 'An error occurred. Please try again.';
        }
    });
}

/**
 * Logout function
 */
function logout() {
    fetch('/logout', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Expose functions to global scope for onclick handlers
window.closeLoginModal = closeLoginModal;

// Mode toggle functions
function toggleSelect() {
    isSelectMode = !isSelectMode;
    isMarkMode = false;
    isDeleteMode = false;

    // Use the universal button IDs without videoId suffix
    const selectBtn = document.getElementById(`selectBtn`);
    const AddBtn = document.getElementById(`AddBtn`);
    const DeleteBtn = document.getElementById(`DeleteBtn`);

    if (!selectBtn || !AddBtn || !DeleteBtn) {
        console.error('Button elements not found');
        return;
    }

    selectBtn.classList.toggle('active');
    AddBtn.classList.remove('active');
    DeleteBtn.classList.remove('active');

    if (isSelectMode) {
        MouseMode = 'c';
        document.querySelectorAll('.video-container').forEach(el => {
            el.style.touchAction = 'none';
        });
        removeEventListeners('A');
        removeEventListeners('B');
        setupEventListeners('A');
        setupEventListeners('B');
    } else {
        clearPreviewBox('A')
        clearPreviewBox('B')
        MouseMode = 'y';
        document.querySelectorAll('.video-container').forEach(el => {
            el.style.touchAction = '';
        });
        removeEventListeners('A');
        removeEventListeners('B');
    }
}

function toggleAddSlot() {
    isMarkMode = !isMarkMode;
    isSelectMode = false;
    isDeleteMode = false;

    // Use the universal button IDs without videoId suffix
    const selectBtn = document.getElementById(`selectBtn`);
    const AddBtn = document.getElementById(`AddBtn`);
    const DeleteBtn = document.getElementById(`DeleteBtn`);

    if (!selectBtn || !AddBtn || !DeleteBtn) {
        console.error('Button elements not found');
        return;
    }

    AddBtn.classList.toggle('active');
    selectBtn.classList.remove('active');
    DeleteBtn.classList.remove('active');

    if (isMarkMode) {
        MouseMode = 'o';
        document.querySelectorAll('.video-container').forEach(el => {
            el.style.touchAction = 'none';
        });
        removeEventListeners('A');
        removeEventListeners('B');
        setupEventListeners('A');
        setupEventListeners('B');
        clearPreviewBox('A')
        clearPreviewBox('B')
    } else {
        MouseMode = 'y';
        document.querySelectorAll('.video-container').forEach(el => {
            el.style.touchAction = '';
        });
        removeEventListeners('A');
        removeEventListeners('B');
    }
}

function toggleDeleteSlot() {
    isMarkMode = false;
    isSelectMode = false;
    isDeleteMode = !isDeleteMode;

    // Use the universal button IDs without videoId suffix
    const selectBtn = document.getElementById(`selectBtn`);
    const AddBtn = document.getElementById(`AddBtn`);
    const DeleteBtn = document.getElementById(`DeleteBtn`);

    if (!selectBtn || !AddBtn || !DeleteBtn) {
        console.error('Button elements not found');
        return;
    }

    DeleteBtn.classList.toggle('active');
    selectBtn.classList.remove('active');
    AddBtn.classList.remove('active');
    
    if (isDeleteMode) {
        MouseMode = 'd';
        document.querySelectorAll('.video-container').forEach(el => {
            el.style.touchAction = 'none';
        });
        removeEventListeners('A');
        removeEventListeners('B');
        setupEventListeners('A');
        setupEventListeners('B');
        clearPreviewBox('A')
        clearPreviewBox('B')
    } else {
        MouseMode = 'y';
        document.querySelectorAll('.video-container').forEach(el => {
            el.style.touchAction = '';
        });
        removeEventListeners('A');
        removeEventListeners('B');
    }
}

// API communication functions - modified to apply to all videos
function setMouseMode(mode) {
    // Apply to video A
    fetch('/set_mouse_mode', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            mode: mode,
            videoId: 'A'
        })
    })
    .then(res => res.json())
    .then(data => {
        // if (data.success) {
        //     showMessage(data.message);
        // }
    })
    .catch(error => {
        console.error('Error setting mode for video A:', error);
    });

    // Apply to video B
    fetch('/set_mouse_mode', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            mode: mode,
            videoId: 'B'
        })
    })
    .then(res => res.json())
    .then(data => {
        // if (data.success) {
        //     showMessage(data.message);
        // }
    })
    .catch(error => {
        console.error('Error setting mode for video B:', error);
    });
}

function refreshVideoFeed(videoId) {
    const video = document.getElementById(`video_stream${videoId}`);
    const timestamp = new Date().getTime();
    video.src = `/video_feed/${videoId}?${timestamp}`;
}

function promptForSlotInput(videoId) {
    const slots = prompt('Enter number of parking slots:');
    if (slots) {
        fetch('/set_slot_input', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                slots: parseInt(slots),
                videoId: videoId
            })
        })
        .then(res => res.json())
        .then(data => {
            // if (data.success) {
            //     showMessage(data.message);
            // }
        });
    }
}

// Mouse handling functions
function getMousePosition(event, canvas) {
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    return {
        x: Math.floor((event.clientX - rect.left) * scaleX),
        y: Math.floor((event.clientY - rect.top) * scaleY)
    };
}

function sendMouseEvent(eventType, x, y, videoId) {
    const requestData = { 
        event: eventType, 
        x: x, 
        y: y,
        videoId: videoId,
        mouse_mode: MouseMode
    };
    
    console.log('Sending mouse event:', requestData);
    
    fetch('/mouse_event', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
    })
    .then(res => {
        if (!res.ok) {
            throw new Error(`Server returned ${res.status}: ${res.statusText}`);
        }
        return res.json();
    })
    .then(data => {
        if (data.success) {
            // if (data.message) showMessage(data.message);
            if (data.action === 'request_slot_input') promptForSlotInput(data.videoId);
            if (data.box) drawPreviewBox(data.box, videoId);
        }
    })
    .catch(error => {
        console.error('Error sending mouse event:', error);
    });
}

function drawPreviewBox(box, videoId) {
    const canvas = document.getElementById(`overlay-canvas${videoId}`);
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.strokeStyle = 'red';
    ctx.lineWidth = 2;
    ctx.strokeRect(
        box.start[0],
        box.start[1],
        box.end[0] - box.start[0],
        box.end[1] - box.start[1]
    );
}

function clearPreviewBox(videoId) {
    const canvas = document.getElementById(`overlay-canvas${videoId}`);
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
}

// Event listeners setup
function setupEventListeners(videoId) {
    const canvas = document.getElementById(`overlay-canvas${videoId}`);
    if (!canvas) return;

    // Buat objek handler
    const handlers = {
        mousedown: function(event) {
            event.preventDefault();
            const pos = getMousePosition(event, canvas);
            mousePressed = true;
            sendMouseEvent('mousedown', pos.x, pos.y, videoId);
        },
        mousemove: function(event) {
            event.preventDefault();
            if (mousePressed) {
                const pos = getMousePosition(event, canvas);
                sendMouseEvent('mousemove', pos.x, pos.y, videoId);
            }
        },
        mouseup: function(event) {
            event.preventDefault();
            const pos = getMousePosition(event, canvas);
            mousePressed = false;
            sendMouseEvent('mouseup', pos.x, pos.y, videoId);
        },
        click: function(event) {
            event.preventDefault();
            const pos = getMousePosition(event, canvas);
            sendMouseEvent('click', pos.x, pos.y, videoId);
        },
        touchstart: function(event) {
            event.preventDefault();
            const touch = event.touches[0];
            const pos = getTouchPosition(touch, canvas);
            mousePressed = true;
            sendMouseEvent('mousedown', pos.x, pos.y, videoId);
        },
        touchmove: function(event) {
            event.preventDefault();
            if (mousePressed) {
                const touch = event.touches[0];
                const pos = getTouchPosition(touch, canvas);
                sendMouseEvent('mousemove', pos.x, pos.y, videoId);
            }
        },
        touchend: function(event) {
            event.preventDefault();
            const touch = event.changedTouches[0];
            const pos = getTouchPosition(touch, canvas);
            mousePressed = false;
            // sendMouseEvent('mouseup', pos.x, pos.y, videoId);
            sendMouseEvent('click', pos.x, pos.y, videoId);
        }
    };

    // Simpan handler untuk remove nanti
    canvasEventHandlers[videoId] = handlers;

    // Pasang listener
    canvas.addEventListener('mousedown', handlers.mousedown);
    canvas.addEventListener('mousemove', handlers.mousemove);
    canvas.addEventListener('mouseup', handlers.mouseup);
    canvas.addEventListener('click', handlers.click);
    canvas.addEventListener('touchstart', handlers.touchstart, { passive: false });
    canvas.addEventListener('touchmove', handlers.touchmove, { passive: false });
    canvas.addEventListener('touchend', handlers.touchend, { passive: false });
}

function removeEventListeners(videoId) {
    const canvas = document.getElementById(`overlay-canvas${videoId}`);
    const handlers = canvasEventHandlers[videoId];
    if (!canvas || !handlers) return;

    canvas.removeEventListener('mousedown', handlers.mousedown);
    canvas.removeEventListener('mousemove', handlers.mousemove);
    canvas.removeEventListener('mouseup', handlers.mouseup);
    canvas.removeEventListener('click', handlers.click);
    canvas.removeEventListener('touchstart', handlers.touchstart, { passive: false });
    canvas.removeEventListener('touchmove', handlers.touchmove, { passive: false });
    canvas.removeEventListener('touchend', handlers.touchend, { passive: false });

    // Hapus dari object global
    delete canvasEventHandlers[videoId];
}

function DeleteSlot(spotId) {
    fetch('/api/parking/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ spotId: spotId })
    })
    .then(r => r.json())
    .then(data => {
        if (data.success) {
            refreshSpots('parking_spots_A', 'A');
            refreshSpots('parking_spots_B', 'B');
        }
    });
}

function createSpotElement(spot) {
    const div = document.createElement("div");
    div.className = `basis-[17%] bg-white rounded-lg shadow-md ${isAdmin ? 'w-30' : 'w-20'} h-20 flex flex-col items-center justify-center`;

    const span = document.createElement("span");
    span.className = "font-bold";
    span.textContent = spot.id;

    // Flex row untuk tombol status dan trash
    const btnContainer = document.createElement("div");
    btnContainer.className = "flex flex-row items-center gap-2 mt-1";

    // Button status
    const btn = document.createElement("button");
    btn.className = `text-xs rounded px-3 py-1 text-white ${
      spot.status === "Tersedia" ? "bg-green-600" : "bg-red-600"
    }`;
    btn.textContent = spot.status;

    // Button trash
    const trashBtn = document.createElement("button");
    trashBtn.className = "bg-red-600 rounded p-1 flex items-center justify-center";
    trashBtn.title = "Hapus spot";
    trashBtn.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22m-5-4v2a2 2 0 01-2 2H9a2 2 0 01-2-2V3h10z" />
        </svg>
    `;
    trashBtn.addEventListener("click", function(e) {
        e.stopPropagation();
        DeleteSlot(spot.id);
    });

    btnContainer.appendChild(btn);
    if(isAdmin){
        btnContainer.appendChild(trashBtn);
    }

    div.appendChild(span);
    div.appendChild(btnContainer);
    return div;
}


function renderSpots(sectionId, spots) {
    const container = document.getElementById(sectionId);
    container.innerHTML = "";
    spots.forEach(spot => {
        container.appendChild(createSpotElement(spot));
    });
}

function refreshSpots(sectionId, videoId) {
    fetch(`/api/parking/${videoId}`)
    .then(r => r.json())
    .then(({spots}) => renderSpots(sectionId, spots))
    .catch(console.error);
}

function initializeCanvas(videoId) {
    const canvas = document.getElementById(`overlay-canvas${videoId}`);
    if (canvas) {
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.pointerEvents = 'all';
        canvas.style.cursor = 'crosshair';
    }
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize canvases
    initializeCanvas('A');
    initializeCanvas('B');
    
    // Setup event listeners
    // setupEventListeners('A');
    // setupEventListeners('B');
    
    // Initial stats update
    updateStats();
    
    // Initial spots refresh
    refreshSpots("spots-a", "A");
    refreshSpots("spots-b", "B");
    
    // Set up intervals for refreshing data
    // setInterval(updateStats, 1000);
    // setInterval(() => refreshSpots("spots-a", "A"), 1200);
    // setInterval(() => refreshSpots("spots-b", "B"), 1200);
});

function updateStats() {
    fetch('/get_stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('spotsA').textContent = `Spots Tersedia: ${data.A.available_slot}`;
            document.getElementById('inA').textContent = `Kendaraan Masuk: ${data.A.vehicles_entered}`;
            document.getElementById('outA').textContent = `Kendaraan Keluar: ${data.A.vehicles_leaved}`;
            
            document.getElementById('spotsB').textContent = `Spots Tersedia: ${data.B.available_slot}`;
            document.getElementById('inB').textContent = `Kendaraan Masuk: ${data.B.vehicles_entered}`;
            document.getElementById('outB').textContent = `Kendaraan Keluar: ${data.B.vehicles_leaved}`;
        })
        .catch(error => {
            console.error('user : Error fetching stats:', error);  // Debug log untuk error
        });
}

setInterval(updateStats, 1000);

document.addEventListener('DOMContentLoaded', updateStats);
document.addEventListener("DOMContentLoaded", () => {
    refreshSpots("spots-a", "A");                      
    setInterval(() => refreshSpots("spots-a", "A"), 1200);  
  });

  document.addEventListener("DOMContentLoaded", () => {
    refreshSpots("spots-b", "B");                     
    setInterval(() => refreshSpots("spots-b", "B"), 1200); 
  });

// Helper function to determine which video the cursor is over
function getCurrentVideoId() {
    const mouseX = window.mouseX || 0;
    const mouseY = window.mouseY || 0;
    
    const canvasA = document.getElementById('overlay-canvasA');
    const canvasB = document.getElementById('overlay-canvasB');
    
    if (canvasA) {
        const rectA = canvasA.getBoundingClientRect();
        if (mouseX >= rectA.left && mouseX <= rectA.right && 
            mouseY >= rectA.top && mouseY <= rectA.bottom) {
            lastInteractedVideoId = 'A';
            return 'A';
        }
    }
    
    if (canvasB) {
        const rectB = canvasB.getBoundingClientRect();
        if (mouseX >= rectB.left && mouseX <= rectB.right && 
            mouseY >= rectB.top && mouseY <= rectB.bottom) {
            lastInteractedVideoId = 'B';
            return 'B';
        }
    }
    
    // If we can't determine, use the last interacted video
    return lastInteractedVideoId;
}

// Track mouse position globally and update last interacted video
document.addEventListener('mousemove', function(event) {
    window.mouseX = event.clientX;
    window.mouseY = event.clientY;
    
    // Update lastInteractedVideoId when mouse moves over a canvas
    const canvasA = document.getElementById('overlay-canvasA');
    const canvasB = document.getElementById('overlay-canvasB');
    
    if (canvasA) {
        const rectA = canvasA.getBoundingClientRect();
        if (event.clientX >= rectA.left && event.clientX <= rectA.right && 
            event.clientY >= rectA.top && event.clientY <= rectA.bottom) {
            lastInteractedVideoId = 'A';
        }
    }
    
    if (canvasB) {
        const rectB = canvasB.getBoundingClientRect();
        if (event.clientX >= rectB.left && event.clientX <= rectB.right && 
            event.clientY >= rectB.top && event.clientY <= rectB.bottom) {
            lastInteractedVideoId = 'B';
        }
    }
});

// Helper function to get touch position
function getTouchPosition(touch, canvas) {
    const rect = canvas.getBoundingClientRect();
    return {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top
    };
}
