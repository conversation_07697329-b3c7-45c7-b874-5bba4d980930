import cv2
import time
import threading
import numpy as np

# Path video sama seperti di app.py
videos = {
    'A': {'path': '/home/<USER>/Downloads/my_video-11.mkv', 'frame': None},
    'B': {'path': '/home/<USER>/Downloads/my_video-9.mkv', 'frame': None}
}

# Flag untuk menghentikan thread
stop_threads = False

# Fungsi untuk membaca video dalam thread terpisah
def read_video(video_id):
    cap = cv2.VideoCapture(videos[video_id]['path'])
    if not cap.isOpened():
        print(f"Error: Tidak dapat membuka video {video_id} di {videos[video_id]['path']}")
        return
    
    print(f"Video {video_id} berhasil dibuka")
    
    while not stop_threads:
        ret, frame = cap.read()
        if not ret:
            print(f"Video {video_id} selesai atau error, mengulang dari awal")
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Ke<PERSON><PERSON> ke awal video
            continue
        
        # Tambahkan label untuk identifikasi
        cv2.putText(frame, f"Camera {video_id}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
        
        # Simpan frame terbaru
        videos[video_id]['frame'] = frame.copy()
        
        # Sedikit delay untuk mengurangi penggunaan CPU
        time.sleep(0.01)
    
    cap.release()
    print(f"Thread video {video_id} berhenti")

# Jalankan thread untuk membaca setiap video
threads = []
for video_id in videos:
    thread = threading.Thread(target=read_video, args=(video_id,))
    thread.daemon = True
    threads.append(thread)
    thread.start()
    print(f"Thread untuk video {video_id} dimulai")

# Buat window di thread utama
cv2.namedWindow("Camera A", cv2.WINDOW_NORMAL)
cv2.resizeWindow("Camera A", 640, 480)
cv2.moveWindow("Camera A", 50, 50)

cv2.namedWindow("Camera B", cv2.WINDOW_NORMAL)
cv2.resizeWindow("Camera B", 640, 480)
cv2.moveWindow("Camera B", 700, 50)

print("Window dibuat, menampilkan video...")

# Tampilkan video di thread utama
try:
    while True:
        # Tampilkan frame dari video A jika tersedia
        if videos['A']['frame'] is not None:
            cv2.imshow("Camera A", videos['A']['frame'])
        
        # Tampilkan frame dari video B jika tersedia
        if videos['B']['frame'] is not None:
            cv2.imshow("Camera B", videos['B']['frame'])
        
        # Keluar jika tombol 'q' ditekan
        if cv2.waitKey(30) & 0xFF == ord('q'):
            break
except KeyboardInterrupt:
    print("Program dihentikan oleh user")
finally:
    # Berhenti membaca video
    stop_threads = True
    
    # Tunggu semua thread selesai
    for thread in threads:
        thread.join(timeout=1.0)
    
    # Tutup semua window
    cv2.destroyAllWindows()
    print("Program selesai")
