import cv2
import pandas as pd
from ultralytics import YOLO
import cvzone
from tracker import*  # Ensure the Tracker class is defined correctly in tracker.py
import tkinter as tk
from tkinter import simpledialog
import os
import time

# Global variables
input_slot = 0
mouse_pressed = False
prev_mouse_pressed = False
box_start = (0, 0)
box_end = (0, 0)
# boxes = []
boxes2 = []
mouse_active = False
corner_points = []  # Store corner points for "o" functionality
mouse_mode = "y"  # Track active mouse mode
vehicles_entered, vehicles_leaved = 0, 0
tracked_ids = set() 
id_entered = set()  # Set untuk menyimpan ID yang masuk
id_leaved = set()   # Set untuk menyimpan ID yang keluar
cropping_area = (0,0,0,0)
cropping_mode = False
crop_start = (0, 0)
crop_end = (0, 0)
count = 0
car_count = 0
bus_count = 0
truck_count = 0
tracker = Tracker()
cy1 = 184
cy2 = 209
offset = 8
loop_time = 0
id_age_dict = {}

def is_point_in_box(px, py, box_coords):
    # Unpack box coordinates
    x1, y1, x2, y2, x3, y3, x4, y4 = box_coords
    # Helper function to calculate cross product of two vectors
    def cross_product(x1, y1, x2, y2, x3, y3):
        return (x2 - x1) * (y3 - y1) - (y2 - y1) * (x3 - x1)

    # Check if the point is on the same side of all edges of the quadrilateral
    d1 = cross_product(x1, y1, x2, y2, px, py)
    d2 = cross_product(x2, y2, x3, y3, px, py)
    d3 = cross_product(x3, y3, x4, y4, px, py)
    d4 = cross_product(x4, y4, x1, y1, px, py)

    # If the signs of all cross products are the same, the point is inside
    return (d1 >= 0 and d2 >= 0 and d3 >= 0 and d4 >= 0) or (d1 <= 0 and d2 <= 0 and d3 <= 0 and d4 <= 0)

def get_user_input():
    global input_slot
    root = tk.Tk()
    root.withdraw()  # Hide the main Tkinter window
    input_slot = simpledialog.askinteger("Input", "Masukkan jumlah slot yang tersedia:")
    root.destroy()

# def init_box():
#     global boxes
#     boxes = []
#     if os.path.exists("data.ul"):
#         with open("data.ul", "r") as file:
#             for line in file:
#                 x1, y1, x2, y2 = map(int, line.strip().split(","))
#                 boxes.append((x1, y1, x2, y2))
            

def init_box2():
    global boxes2
    boxes2 = []
    if os.path.exists("data2.ul"):
        with open("data2.ul", "r") as file:
            for line in file:
                x1, y1, x2, y2, x3, y3, x4, y4 = map(int, line.strip().split(","))
                boxes2.append((x1, y1, x2, y2, x3, y3, x4, y4))

def init_crop_area():
    global tracker, vehicles_entered, vehicles_leaved, tracked_ids, cropping_area, cropping_mode
    tracker.reset()
    vehicles_entered, vehicles_leaved = 0, 0
    tracked_ids = set() 
    if os.path.exists("crop.ul"):
        cropping_mode = True
        with open("crop.ul", "r") as file:
            for line in file:
                x1, y1, x2, y2 = map(int, line.strip().split(","))
                cropping_area = (x1, y1, x2, y2)
    else:
        cropping_mode = False

# def save_box():
#     if not os.path.exists("data.ul"):
#         with open("data.ul", "w") as file:
#             for x in range(input_slot):
#                 file.write(f"{box_start[0] + (box_end[0] - box_start[0])//input_slot * x},{box_start[1]},{box_start[0] + (box_end[0] - box_start[0])//input_slot * (x+1)},{box_end[1]}\n")
#     else:
#         with open("data.ul", "a") as file:
#             for x in range(input_slot):
#                 file.write(f"{box_start[0] + (box_end[0] - box_start[0])//input_slot * x},{box_start[1]},{box_start[0] + (box_end[0] - box_start[0])//input_slot * (x+1)},{box_end[1]}\n")

def sort_corner_points(points):
    # Mencari titik tengah
    center_x = sum(p[0] for p in points) / 4
    center_y = sum(p[1] for p in points) / 4
    
    # Memisahkan titik atas dan bawah berdasarkan y
    top_points = []
    bottom_points = []
    for point in points:
        if point[1] < center_y:
            top_points.append(point)
        else:
            bottom_points.append(point)
    
    # Mengurutkan titik atas (kiri ke kanan)
    top_points.sort(key=lambda p: p[0])
    # Mengurutkan titik bawah (kanan ke kiri)
    bottom_points.sort(key=lambda p: p[0], reverse=True)
    
    # Mengembalikan urutan: kiri atas, kanan atas, kanan bawah, kiri bawah
    return [top_points[0], top_points[1], bottom_points[0], bottom_points[1]]

def save_box2():
    if len(corner_points) == 4:
        # Mengurutkan corner points sebelum memproses
        sorted_corners = sort_corner_points(corner_points)
        
        if not os.path.exists("data2.ul"):
            with open("data2.ul", "w") as file:
                for x in range(input_slot):
                    x1 = sorted_corners[0][0] + (sorted_corners[1][0] - sorted_corners[0][0]) // input_slot * x
                    x2 = sorted_corners[0][0] + (sorted_corners[1][0] - sorted_corners[0][0]) // input_slot * (x + 1)
                    x3 = sorted_corners[3][0] + (sorted_corners[2][0] - sorted_corners[3][0]) // input_slot * (x + 1)
                    x4 = sorted_corners[3][0] + (sorted_corners[2][0] - sorted_corners[3][0]) // input_slot * x
                    y1 = sorted_corners[0][1] + (sorted_corners[1][1] - sorted_corners[0][1]) // input_slot * x
                    y2 = sorted_corners[0][1] + (sorted_corners[1][1] - sorted_corners[0][1]) // input_slot * (x + 1)
                    y3 = sorted_corners[3][1] + (sorted_corners[2][1] - sorted_corners[3][1]) // input_slot * (x + 1)
                    y4 = sorted_corners[3][1] + (sorted_corners[2][1] - sorted_corners[3][1]) // input_slot * x
                    file.write(f"{x1},{y1},{x2},{y2},{x3},{y3},{x4},{y4}\n")
        else:
            with open("data2.ul", "a") as file:
                for x in range(input_slot):
                    x1 = sorted_corners[0][0] + (sorted_corners[1][0] - sorted_corners[0][0]) // input_slot * x
                    x2 = sorted_corners[0][0] + (sorted_corners[1][0] - sorted_corners[0][0]) // input_slot * (x + 1)
                    x3 = sorted_corners[3][0] + (sorted_corners[2][0] - sorted_corners[3][0]) // input_slot * (x + 1)
                    x4 = sorted_corners[3][0] + (sorted_corners[2][0] - sorted_corners[3][0]) // input_slot * x
                    y1 = sorted_corners[0][1] + (sorted_corners[1][1] - sorted_corners[0][1]) // input_slot * x
                    y2 = sorted_corners[0][1] + (sorted_corners[1][1] - sorted_corners[0][1]) // input_slot * (x + 1)
                    y3 = sorted_corners[3][1] + (sorted_corners[2][1] - sorted_corners[3][1]) // input_slot * (x + 1)
                    y4 = sorted_corners[3][1] + (sorted_corners[2][1] - sorted_corners[3][1]) // input_slot * x
                    file.write(f"{x1},{y1},{x2},{y2},{x3},{y3},{x4},{y4}\n")

def save_cropped_size():
    with open("crop.ul", "w") as file:
        file.write(f"{box_start[0]},{box_start[1]},{box_end[0]},{box_end[1]}\n")
    init_crop_area()
    
def is_obj_in_box(square_data):
    for bbox in cars_boxes:
        # cv2.circle(frame, [(bbox[0] + (bbox[2] - bbox[0])//2), (bbox[3] - (bbox[3] - bbox[1])//5)], 5, (0, 255, 255), 2)
        # cv2.circle(frame, [square_data[0], square_data[1]], 5, (0, 255, 255), 5)
        if is_point_in_box((bbox[0] + (bbox[2] - bbox[0])//2), (bbox[3] - (bbox[3] - bbox[1])//5),square_data):
            return True
        else:
            pass
    return False
    

# Load the YOLO model
# model = YOLO('yolov8s_ncnn_model')
model = YOLO('yolov8n.pt')

def RGB(event, x, y, flags, param):
    global mouse_pressed, prev_mouse_pressed, box_start, box_end, input_slot, mouse_active, corner_points, mouse_mode, cropping_mode

    if not mouse_active:
        return

    # if mouse_mode == "p":
    #     if event == cv2.EVENT_LBUTTONDOWN:
    #         mouse_pressed = True
    #         box_start = (x, y)

    #     elif event == cv2.EVENT_MOUSEMOVE:
    #         if mouse_pressed:
    #             box_end = (x, y)

    #     elif event == cv2.EVENT_LBUTTONUP:
    #         mouse_pressed = False
    #         box_end = (x, y)
    #         get_user_input()
    #         save_box()
    #         init_box()

    elif mouse_mode == "o":
        if event == cv2.EVENT_LBUTTONUP:
            corner_points.append((x, y))
            if len(corner_points) == 4:
                get_user_input()
                save_box2()
                init_box2()
                corner_points.clear()
    
    elif mouse_mode == "c":
        if event == cv2.EVENT_LBUTTONDOWN:
            mouse_pressed = True
            box_start = (x, y)

        elif event == cv2.EVENT_MOUSEMOVE:
            if mouse_pressed:
                box_end = (x, y)
                

        elif event == cv2.EVENT_LBUTTONUP:
            mouse_pressed = False
            box_end = (x, y)
            save_cropped_size()
            # cropping_area = (box_start[0], box_start[1], box_end[0], box_end[1])
            cropping_mode = True
            # global tracker, vehicles_entered, vehicles_leaved, tracked_ids
            # tracker.reset()
            # vehicles_entered, vehicles_leaved = 0, 0
            # tracked_ids = set() 

# def process_objects(objects):
#     global vehicles_entered, vehicles_leaved
    
#     for obj in objects:
#         id = obj[4]         # Ambil ID
#         age_obj = obj[5]    # Ambil age_obj
        
#         # Jika ID belum pernah diproses, tambahkan ke dictionary
#         if id not in id_age_dict:
#             id_age_dict[id] = age_obj
            
#             # Logika untuk vehicles_entered
#             if age_obj > 5:
#                 vehicles_entered += 1
#                 # print(f"ID {id} entered. Total vehicles entered: {vehicles_entered}")
        
#         else:
#             # Periksa jika age_obj == 0 untuk mendeteksi leave
#             if age_obj > 5 and id_age_dict[id] > 0:
#                 vehicles_leaved += 1
#                 # print(f"ID {id} leaved. Total vehicles leaved: {vehicles_leaved}")
                
#                 # Hapus ID dari dictionary setelah meninggalkan area
#                 del id_age_dict[id]
#             else:
#                 # Update umur ID jika masih dalam area
#                 id_age_dict[id] = age_obj            

# Initialize boxes from file
# init_box()
init_box2()
init_crop_area()
# Create a named window and set a mouse callback function
cv2.namedWindow('RGB')
cv2.setMouseCallback('RGB', RGB)

gst_pipeline1 = (
    "v4l2src device=/dev/video0 ! "
    "image/jpeg, width=640, height=480, framerate=30/1 ! "
    "jpegdec ! videoconvert ! appsink"
)

gst_pipeline2 = (
    "v4l2src device=/dev/video2 ! "
    "video/x-h264, width=640, height=480, framerate=30/1 ! "
    "h264parse ! avdec_h264 ! videoconvert ! appsink"
)

# cap = cv2.VideoCapture(gst_pipeline1, cv2.CAP_GSTREAMER)
# cap = cv2.VideoCapture(gst_pipeline2, cv2.CAP_GSTREAMER)
cap = cv2.VideoCapture('/home/<USER>/my_video-11.mkv')  # car_countInitialize video capture with the video file


with open("coco.txt", "r") as my_file:
    class_list = my_file.read().split("\n")



while True:
    ret, frame = cap.read()
    if not ret:
        break
    count += 1
    if count % 3 != 0:
        continue
    print('FPS {}'.format(1 / (time.time() - loop_time)))
    loop_time = time.time()
    # frame = cv2.imread("image3.jpeg", cv2.IMREAD_COLOR)
    if cropping_mode:
        x1_crop, y1_crop, x2_crop, y2_crop = cropping_area
        cropped_frame = frame[y1_crop:y2_crop, x1_crop:x2_crop]
        if cropped_frame is None or cropped_frame.size == 0:
            print("Warning: Cropped frame is empty. Skipping inference...")
            continue
        else:
            # Lanjutkan jika gambar valid
            results = model.predict(cropped_frame)
    else:
        cropped_frame = frame

    results = model.predict(cropped_frame)
    detections = results[0].boxes.data
    px = pd.DataFrame(detections).astype("float")

    cars = []

    for index, row in px.iterrows():
        x1 = int(cropping_area[0] + row[0])
        y1 = int(cropping_area[1] + row[1])
        x2 = int(cropping_area[0] + row[2])
        y2 = int(cropping_area[1] + row[3])
        d = int(row[5])
        confidence = float(row[4])
        c = class_list[d]
        if confidence >= 0.4:
            if 'car' in c:
                cars.append([x1, y1, x2, y2, confidence])
            elif 'bus' in c:
                cars.append([x1, y1, x2, y2, confidence])
            elif 'truck' in c:
                cars.append([x1, y1, x2, y2, confidence])

    cars_boxes = tracker.update(cars)
    
    for bbox in cars_boxes:
        cx = int((bbox[0] + bbox[2]) / 2)
        cy = int((bbox[1] + bbox[3]) / 2)
        if (cy > cy1 - offset) and (cy < cy1 + offset):
            car_count += 1

    for bbox in cars_boxes:
        cx = int((bbox[0] + bbox[2]) / 2)
        cy = int((bbox[1] + bbox[3]) / 2)
        cv2.putText(frame, f'ID: {bbox[5]}', (cx, cy), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 125, 255), 3)
        cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 255), 2)
        cvzone.putTextRect(frame, f'{bbox[5]}, {bbox[4]:.2f}', (bbox[0], bbox[1]), 1, 1)

    index = 0
    prev = False
    for park_slot in boxes2:
        if is_obj_in_box(park_slot):
            prev = True
            cv2.line(frame, (park_slot[0], park_slot[1]), (park_slot[6], park_slot[7]), (255, 0, 0), 3)
            for x in range(0, 5, 2):
                cv2.line(frame, (park_slot[x], park_slot[x+1]), (park_slot[x+2], park_slot[x+3]), (255, 0, 0), 3)
        
        else:
            if prev == False:
                cv2.line(frame, (park_slot[0], park_slot[1]), (park_slot[6], park_slot[7]), (0, 0, 255), 3)
            else:
                cv2.line(frame, (park_slot[0], park_slot[1]), (park_slot[6], park_slot[7]), (255, 0, 0), 3)
            prev = False
            for x in range(0, 5, 2):
                cv2.line(frame, (park_slot[x], park_slot[x+1]), (park_slot[x+2], park_slot[x+3]), (0, 0, 255), 3)
        
        cv2.putText(frame, str(index), (park_slot[6], park_slot[7]), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2, cv2.LINE_AA)
        index += 1

    if mouse_pressed:
        cv2.rectangle(frame, box_start, box_end, (0, 255, 0), 2)

    for point in corner_points:
        cv2.circle(frame, point, 5, (0, 0, 255), -1)

    # current_ids = set(obj[4] for obj in cars_boxes)
    # current_ids sekarang berisi tuple (obj[4], obj[5])
    current_ids = set((obj[5], obj[6]) for obj in cars_boxes)
    current_id_only = set(obj[5] for obj in cars_boxes)  # Set yang hanya berisi ID

    # Masuk kendaraan baru
    for obj_id, age_obj in current_ids:
        if obj_id not in id_entered and age_obj > 5:
            id_entered.add(obj_id)  # Tambahkan ke set ID yang masuk
            vehicles_entered = len(id_entered)  # Update jumlah kendaraan masuk
            print(f"ID {obj_id} entered. Total vehicles entered: {vehicles_entered}")
    
    # Kendaraan yang keluar - hanya jika ID benar-benar tidak ada lagi
    for obj_id in list(id_entered):
        if obj_id not in current_id_only:  # ID tidak ada di frame saat ini
            if obj_id not in id_leaved:  # dan belum tercatat sebagai keluar
                id_leaved.add(obj_id)  # Tambahkan ke set ID yang keluar
                vehicles_leaved = len(id_leaved)  # Update jumlah kendaraan keluar
                print(f"ID {obj_id} leaved. Total vehicles leaved: {vehicles_leaved}")
    
    # Jika ID yang sudah tercatat keluar muncul lagi, hapus dari daftar keluar
    for obj_id in list(id_leaved):
        if obj_id in current_id_only:
            id_leaved.remove(obj_id)
            vehicles_leaved = len(id_leaved)  # Update jumlah kendaraan keluar
            print(f"ID {obj_id} returned. Total vehicles leaved: {vehicles_leaved}")

    # process_objects(cars_boxes)

    # Tampilkan hasil
    cv2.putText(frame, f"Masuk: {vehicles_entered}", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    cv2.putText(frame, f"Keluar: {vehicles_leaved}", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)


    cv2.imshow("RGB", frame)
    key = cv2.waitKey(1) & 0xFF
    time.sleep(0.06)
    # if key == ord('p'):
    #     mouse_active = not mouse_active
    #     mouse_mode = "p"
    if key == ord('o'):
        mouse_active = not mouse_active
        mouse_mode = "o"
    if key == 27:
        break
    if key == ord('c'):
        mouse_active = not mouse_active
        mouse_mode = "c"

cap.release()
cv2.destroyAllWindows()
