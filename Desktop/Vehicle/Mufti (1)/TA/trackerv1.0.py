import math


class Tracker:
    def __init__(self):
        # Store the center positions of the objects
        self.center_points = {}
        # Keep the count of the IDs
        self.id_count = 0
        # Dictionary to store object ages
        self.object_ages = {}
        # Dictionary to track disappearance counts
        self.disappearance_counts = {}

    def update(self, objects_rect):
        # Objects boxes and ids
        objects_bbs_ids = []
        
        # Track current detected object IDs
        current_object_ids = set()

        # Get center point of new object
        for rect in objects_rect:
            x1, y1, x2, y2, conv = rect
            cx = (x1 + x2) // 2
            cy = (y1 + y2) // 2

            # Find out if that object was detected already
            same_object_detected = False
            for object_id, pt in self.center_points.items():
                dist = math.hypot(cx - pt[0], cy - pt[1])

                # Check if the distance is less than a threshold (e.g., 50 pixels)
                if dist < 100:
                    # Update the center point of the detected object
                    self.center_points[object_id] = (cx, cy)
                    # Increment age for existing object
                    self.object_ages[object_id] = self.object_ages.get(object_id, 0) + 1
                    # Reset disappearance count since object is detected
                    self.disappearance_counts[object_id] = 0
                    # Add box with ID and age
                    objects_bbs_ids.append([x1, y1, x2, y2, conv, object_id, self.object_ages[object_id]])
                    same_object_detected = True
                    current_object_ids.add(object_id)
                    break

            # If the object is not detected, assign a new ID
            if not same_object_detected:
                self.center_points[self.id_count] = (cx, cy)
                # Initialize age for new object
                self.object_ages[self.id_count] = 1
                # Initialize disappearance count
                self.disappearance_counts[self.id_count] = 0
                # Add box with ID and initial age
                objects_bbs_ids.append([x1, y1, x2, y2, conv, self.id_count, 1])
                current_object_ids.add(self.id_count)
                self.id_count += 1

        # Handle objects that weren't detected in this frame
        for object_id in list(self.center_points.keys()):
            if object_id not in current_object_ids:
                # If object age > 10, keep it for up to 10 cycles
                if self.object_ages[object_id] > 10:
                    self.disappearance_counts[object_id] = self.disappearance_counts.get(object_id, 0) + 1
                    
                    # If disappeared for less than 10 cycles, keep it
                    if self.disappearance_counts[object_id] <= 10:
                        # Add the object to results with its last known position
                        x1, y1 = self.center_points[object_id][0] - 50, self.center_points[object_id][1] - 50  # Approximate
                        x2, y2 = self.center_points[object_id][0] + 50, self.center_points[object_id][1] + 50  # Approximate
                        objects_bbs_ids.append([x1, y1, x2, y2, 0, object_id, self.object_ages[object_id]])
                        current_object_ids.add(object_id)

        # Clean the dictionaries by keeping only IDs that are still in the frame or being preserved
        new_center_points = {}
        new_object_ages = {}
        new_disappearance_counts = {}
        
        # Keep objects that are either detected or being preserved
        for object_id in list(self.center_points.keys()):
            if (object_id in current_object_ids) or (self.object_ages.get(object_id, 0) > 10 and self.disappearance_counts.get(object_id, 0) <= 10):
                new_center_points[object_id] = self.center_points[object_id]
                new_object_ages[object_id] = self.object_ages[object_id]
                new_disappearance_counts[object_id] = self.disappearance_counts.get(object_id, 0)

        self.center_points = new_center_points
        self.object_ages = new_object_ages
        self.disappearance_counts = new_disappearance_counts
        
        return objects_bbs_ids

    def reset(self):
        """Reset all tracking data."""
        self.center_points = {}
        self.id_count = 0
        self.object_ages = {}
        self.disappearance_counts = {}
